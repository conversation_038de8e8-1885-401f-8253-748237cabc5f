/**
** =====================================================================================
**
**       文件名称: texture_manager.h
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】纹理管理系统 - 高性能纹理压缩与流式加载框架 （声明文件）
**
**       主要功能:
**       - 高性能纹理压缩与解压缩
**       - 智能纹理流式加载与卸载
**       - GPU内存池管理与优化
**       - 纹理图集自动生成与管理
**       - 多级细节层次（LOD）支持
**       - 异步纹理加载与预加载
**       - 纹理缓存与生命周期管理
**
**       技术特性:
**       - 采用现代C++17标准与DirectX11 API
**       - 支持DXT/BC纹理压缩格式
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 多线程安全的纹理管理
**       - 自适应内存管理策略
**       - 实时性能监控与统计分析
**
**       更新记录:
**       2025-07-31 v1.0.0.0 : 1. 创建纹理管理系统
**                             2. 实现纹理压缩与流式加载
**                             3. 添加GPU内存池管理
**                             4. 完成纹理图集管理
**                             5. 集成LOD支持
**                             6. 实现异步加载机制
**                             7. 确保多线程安全性
**
** =====================================================================================
**/

#pragma once
#include "render_api.h"
#include "common/smart_ptr.hpp"
#include "common/enhanced_mem_pool.hpp"
#include "common/resource_manager.hpp"
#include <vector>
#include <unordered_map>
#include <queue>
#include <mutex>
#include <atomic>
#include <future>
#include <thread>
#include <DirectXMath.h>
#include <wrl.h>

namespace HHBUI
{
    /// 纹理压缩格式
    enum class TextureFormat : uint32_t
    {
        UNKNOWN = 0,
        R8G8B8A8_UNORM,         // 未压缩RGBA
        R8G8B8A8_SRGB,          // sRGB RGBA
        BC1_UNORM,              // DXT1压缩
        BC1_SRGB,               // DXT1 sRGB
        BC2_UNORM,              // DXT3压缩
        BC2_SRGB,               // DXT3 sRGB
        BC3_UNORM,              // DXT5压缩
        BC3_SRGB,               // DXT5 sRGB
        BC4_UNORM,              // 单通道压缩
        BC5_UNORM,              // 双通道压缩
        BC6H_UF16,              // HDR压缩
        BC7_UNORM,              // 高质量压缩
        BC7_SRGB                // 高质量sRGB压缩
    };

    /// 纹理类型
    enum class TextureType : uint32_t
    {
        TEXTURE_2D = 0,
        TEXTURE_CUBE,
        TEXTURE_ARRAY,
        TEXTURE_3D
    };

    /// 纹理加载优先级
    enum class TextureLoadPriority : uint32_t
    {
        LOW = 0,
        NORMAL,
        HIGH,
        CRITICAL
    };

    /// 纹理描述符
    struct TextureDesc
    {
        uint32_t width;
        uint32_t height;
        uint32_t depth;
        uint32_t mip_levels;
        uint32_t array_size;
        TextureFormat format;
        TextureType type;
        uint32_t bind_flags;
        uint32_t cpu_access_flags;
        uint32_t misc_flags;
        
        TextureDesc() : width(0), height(0), depth(1), mip_levels(1), 
                       array_size(1), format(TextureFormat::R8G8B8A8_UNORM),
                       type(TextureType::TEXTURE_2D), bind_flags(D3D11_BIND_SHADER_RESOURCE),
                       cpu_access_flags(0), misc_flags(0) {}
    };

    /// 纹理资源
    class UITexture
    {
    public:
        UITexture();
        ~UITexture();

        /// 创建纹理
        HRESULT Create(ID3D11Device* device, const TextureDesc& desc, const void* initial_data = nullptr);

        /// 从文件加载纹理
        HRESULT LoadFromFile(ID3D11Device* device, LPCWSTR file_path, bool generate_mips = true);

        /// 从内存加载纹理
        HRESULT LoadFromMemory(ID3D11Device* device, const void* data, size_t data_size, bool generate_mips = true);

        /// 压缩纹理数据
        HRESULT CompressTexture(TextureFormat target_format);

        /// 生成Mipmap
        HRESULT GenerateMipmaps(ID3D11DeviceContext* context);

        /// 获取纹理接口
        ID3D11Texture2D* GetTexture() const { return m_texture.Get(); }
        ID3D11ShaderResourceView* GetSRV() const { return m_srv.Get(); }
        ID3D11RenderTargetView* GetRTV() const { return m_rtv.Get(); }
        ID3D11DepthStencilView* GetDSV() const { return m_dsv.Get(); }

        /// 获取纹理描述符
        const TextureDesc& GetDesc() const { return m_desc; }

        /// 获取内存使用量
        uint32_t GetMemoryUsage() const { return m_memory_usage; }

        /// 是否已加载
        bool IsLoaded() const { return m_texture != nullptr; }

        /// 释放资源
        void Release();

    private:
        Microsoft::WRL::ComPtr<ID3D11Texture2D> m_texture;
        Microsoft::WRL::ComPtr<ID3D11ShaderResourceView> m_srv;
        Microsoft::WRL::ComPtr<ID3D11RenderTargetView> m_rtv;
        Microsoft::WRL::ComPtr<ID3D11DepthStencilView> m_dsv;
        
        TextureDesc m_desc;
        uint32_t m_memory_usage;
        std::atomic<bool> m_is_loading;

        HRESULT CreateViews(ID3D11Device* device);
        uint32_t CalculateMemoryUsage() const;
    };

    /// 纹理加载请求
    struct TextureLoadRequest
    {
        std::wstring file_path;
        TextureLoadPriority priority;
        TextureFormat target_format;
        bool generate_mips;
        std::promise<ExSharedPtr<UITexture>> promise;
        std::chrono::steady_clock::time_point request_time;
        
        TextureLoadRequest() : priority(TextureLoadPriority::NORMAL), 
                              target_format(TextureFormat::R8G8B8A8_UNORM),
                              generate_mips(true),
                              request_time(std::chrono::steady_clock::now()) {}
    };

    /// 纹理缓存项
    struct TextureCacheEntry
    {
        ExSharedPtr<UITexture> texture;
        std::chrono::steady_clock::time_point last_access_time;
        uint32_t reference_count;
        uint32_t memory_usage;
        
        TextureCacheEntry() : last_access_time(std::chrono::steady_clock::now()),
                             reference_count(0), memory_usage(0) {}
    };

    /// GPU内存池
    class UIGPUMemoryPool
    {
    public:
        UIGPUMemoryPool();
        ~UIGPUMemoryPool();

        /// 初始化内存池
        HRESULT Initialize(ID3D11Device* device, uint64_t pool_size = 256 * 1024 * 1024);

        /// 关闭内存池
        void Shutdown();

        /// 分配纹理内存
        HRESULT AllocateTexture(const TextureDesc& desc, ID3D11Texture2D** texture);

        /// 释放纹理内存
        void DeallocateTexture(ID3D11Texture2D* texture);

        /// 获取内存使用统计
        struct MemoryStats
        {
            uint64_t total_size;
            uint64_t used_size;
            uint64_t free_size;
            uint32_t allocation_count;
            uint32_t deallocation_count;
            float fragmentation_ratio;
        };
        const MemoryStats& GetStats() const { return m_stats; }

        /// 执行垃圾回收
        void GarbageCollect();

    private:
        struct MemoryBlock
        {
            uint64_t offset;
            uint64_t size;
            bool is_free;
            ID3D11Texture2D* texture;
        };

        ID3D11Device* m_device;
        std::vector<MemoryBlock> m_blocks;
        MemoryStats m_stats;
        std::mutex m_mutex;

        void CoalesceBlocks();
        MemoryBlock* FindFreeBlock(uint64_t size);
    };

    /// 纹理管理器
    class UITextureManager
    {
    public:
        UITextureManager();
        ~UITextureManager();

        /// 初始化纹理管理器
        HRESULT Initialize(ID3D11Device* device, ID3D11DeviceContext* context);

        /// 关闭纹理管理器
        void Shutdown();

        /// 同步加载纹理
        ExSharedPtr<UITexture> LoadTexture(LPCWSTR file_path,
                                          TextureFormat format = TextureFormat::R8G8B8A8_UNORM,
                                          bool generate_mips = true);

        /// 异步加载纹理
        std::future<ExSharedPtr<UITexture>> LoadTextureAsync(LPCWSTR file_path,
                                                            TextureLoadPriority priority = TextureLoadPriority::NORMAL,
                                                            TextureFormat format = TextureFormat::R8G8B8A8_UNORM,
                                                            bool generate_mips = true);

        /// 预加载纹理
        void PreloadTexture(LPCWSTR file_path, TextureLoadPriority priority = TextureLoadPriority::LOW);

        /// 卸载纹理
        void UnloadTexture(LPCWSTR file_path);

        /// 创建纹理图集
        HRESULT CreateTextureAtlas(const std::vector<std::wstring>& texture_paths,
                                  uint32_t atlas_width, uint32_t atlas_height,
                                  ExSharedPtr<UITexture>& atlas_texture,
                                  std::unordered_map<std::wstring, DirectX::XMFLOAT4>& uv_mappings);

        /// 设置缓存大小限制
        void SetCacheLimit(uint64_t max_memory_bytes, uint32_t max_texture_count = 1000);

        /// 执行缓存清理
        void CleanupCache();

        /// 获取统计信息
        struct TextureStats
        {
            uint32_t loaded_texture_count;
            uint32_t cached_texture_count;
            uint64_t total_memory_usage;
            uint32_t async_load_queue_size;
            uint32_t cache_hit_count;
            uint32_t cache_miss_count;
            float cache_hit_ratio;
        };
        const TextureStats& GetStats() const { return m_stats; }

        /// 获取GPU内存池
        UIGPUMemoryPool* GetMemoryPool() { return &m_memory_pool; }

    private:
        ID3D11Device* m_device;
        ID3D11DeviceContext* m_context;

        // 纹理缓存
        std::unordered_map<std::wstring, TextureCacheEntry> m_texture_cache;

        // 异步加载
        std::queue<TextureLoadRequest> m_load_queue;
        std::vector<std::thread> m_worker_threads;
        std::mutex m_queue_mutex;
        std::condition_variable m_queue_cv;
        std::atomic<bool> m_shutdown_requested;

        // GPU内存池
        UIGPUMemoryPool m_memory_pool;

        // 缓存限制
        uint64_t m_max_cache_memory;
        uint32_t m_max_cache_count;

        // 统计信息
        TextureStats m_stats;
        std::mutex m_stats_mutex;

        // 内部方法
        void WorkerThreadProc();
        void ProcessLoadRequest(TextureLoadRequest& request);
        void UpdateCacheEntry(const std::wstring& key, ExSharedPtr<UITexture> texture);
        void EvictLeastRecentlyUsed();
        uint64_t CalculateTotalCacheMemory() const;
    };

    /// 着色器编译缓存
    class UIShaderCache
    {
    public:
        UIShaderCache();
        ~UIShaderCache();

        /// 初始化着色器缓存
        HRESULT Initialize(LPCWSTR cache_directory);

        /// 关闭着色器缓存
        void Shutdown();

        /// 编译并缓存着色器
        HRESULT CompileShader(LPCWSTR source_file, LPCSTR entry_point, LPCSTR target,
                             const D3D_SHADER_MACRO* defines, ID3DBlob** shader_blob);

        /// 从缓存加载着色器
        HRESULT LoadCachedShader(LPCWSTR source_file, LPCSTR entry_point, LPCSTR target,
                                const D3D_SHADER_MACRO* defines, ID3DBlob** shader_blob);

        /// 清理过期缓存
        void CleanupExpiredCache(uint32_t max_age_days = 30);

        /// 获取缓存统计
        struct CacheStats
        {
            uint32_t total_shaders;
            uint32_t cache_hits;
            uint32_t cache_misses;
            uint64_t total_cache_size;
            float hit_ratio;
        };
        const CacheStats& GetStats() const { return m_stats; }

    private:
        std::wstring m_cache_directory;
        std::unordered_map<std::wstring, std::wstring> m_shader_cache_map;
        CacheStats m_stats;
        std::mutex m_mutex;

        std::wstring GenerateCacheKey(LPCWSTR source_file, LPCSTR entry_point,
                                     LPCSTR target, const D3D_SHADER_MACRO* defines);
        std::wstring GetCacheFilePath(const std::wstring& cache_key);
        HRESULT SaveShaderToCache(const std::wstring& cache_key, ID3DBlob* shader_blob);
        HRESULT LoadShaderFromCache(const std::wstring& cache_key, ID3DBlob** shader_blob);
    };

    /// 全局纹理管理器实例
    extern UITextureManager* g_texture_manager;
    extern UIShaderCache* g_shader_cache;

    /// 便捷宏定义
    #define TEXTURE_LOAD(path) if(g_texture_manager) g_texture_manager->LoadTexture(path)
    #define TEXTURE_LOAD_ASYNC(path, priority) if(g_texture_manager) g_texture_manager->LoadTextureAsync(path, priority)
    #define TEXTURE_PRELOAD(path) if(g_texture_manager) g_texture_manager->PreloadTexture(path)
    #define SHADER_COMPILE_CACHED(file, entry, target, defines, blob) if(g_shader_cache) g_shader_cache->CompileShader(file, entry, target, defines, blob)
}
