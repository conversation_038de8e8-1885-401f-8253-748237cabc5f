/**
** =====================================================================================
**
**       文件名称: render_command_queue.cpp
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】多线程渲染命令队列系统 - 高性能渲染命令管理框架 （实现文件）
**
** =====================================================================================
**/

#include "pch.h"
#include "render_command_queue.h"
#include "common/Exception.h"

namespace HHBUI
{
    // 全局渲染系统实例
    UIRenderCommandQueue* g_render_command_queue = nullptr;
    UIBackgroundResourceLoader* g_background_loader = nullptr;
    UIUpdateRenderSeparator* g_update_render_separator = nullptr;

    // UIRenderCommandQueue 实现
    UIRenderCommandQueue::UIRenderCommandQueue()
        : m_current_frame_id(0)
        , m_enable_sorting(true)
        , m_max_commands(10000)
        , m_stats{}
    {
    }

    UIRenderCommandQueue::~UIRenderCommandQueue()
    {
        Shutdown();
    }

    HRESULT UIRenderCommandQueue::Initialize(uint32_t max_commands)
    {
        try
        {
            m_max_commands = max_commands;
            m_stats = QueueStats{};
            
            return S_OK;
        }
        catch (const Exception& ex)
        {
            return ex.GetStatus();
        }
    }

    void UIRenderCommandQueue::Shutdown()
    {
        std::lock_guard<std::mutex> lock(m_queue_mutex);
        ClearCommands();
        m_stats = QueueStats{};
    }

    void UIRenderCommandQueue::BeginFrame(uint32_t frame_id)
    {
        m_current_frame_id = frame_id;
        
        // 清空上一帧的命令
        ClearCommands();
    }

    void UIRenderCommandQueue::EndFrame()
    {
        m_stats.frames_processed++;
        
        // 计算平均命令数
        if (m_stats.frames_processed > 0)
        {
            m_stats.average_commands_per_frame = 
                static_cast<float>(m_stats.commands_executed) / m_stats.frames_processed;
        }
    }

    void UIRenderCommandQueue::AddCustomCommand(std::function<void()> func, 
                                               RenderCommandPriority priority,
                                               uint64_t sort_key)
    {
        auto command = ExMakeShared<RenderCommand>();
        command->type = RenderCommandType::CUSTOM;
        command->priority = priority;
        command->sort_key = sort_key;
        command->frame_id = m_current_frame_id.load();
        command->execute_func = func;
        
        std::lock_guard<std::mutex> lock(m_queue_mutex);
        
        if (m_command_queue.size() < m_max_commands)
        {
            m_command_queue.push(command);
            m_stats.commands_added++;
        }
    }

    void UIRenderCommandQueue::ExecuteCommands()
    {
        std::lock_guard<std::mutex> lock(m_execution_mutex);
        
        auto start_time = std::chrono::steady_clock::now();
        
        // 将队列中的命令移动到排序向量
        {
            std::lock_guard<std::mutex> queue_lock(m_queue_mutex);
            
            m_sorted_commands.clear();
            m_sorted_commands.reserve(m_command_queue.size());
            
            while (!m_command_queue.empty())
            {
                m_sorted_commands.push_back(m_command_queue.front());
                m_command_queue.pop();
            }
            
            m_stats.commands_pending = 0;
        }
        
        // 排序命令
        if (m_enable_sorting && !m_sorted_commands.empty())
        {
            SortCommands();
        }
        
        // 执行命令
        for (auto& command : m_sorted_commands)
        {
            if (command)
            {
                command->Execute();
                m_stats.commands_executed++;
            }
        }
        
        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        m_stats.average_execution_time = duration;
    }

    void UIRenderCommandQueue::ClearCommands()
    {
        std::lock_guard<std::mutex> lock(m_queue_mutex);
        
        while (!m_command_queue.empty())
        {
            m_command_queue.pop();
        }
        
        m_sorted_commands.clear();
        m_stats.commands_pending = 0;
    }

    void UIRenderCommandQueue::SortCommands()
    {
        std::sort(m_sorted_commands.begin(), m_sorted_commands.end(), CompareCommands);
    }

    bool UIRenderCommandQueue::CompareCommands(const ExSharedPtr<RenderCommand>& a, 
                                              const ExSharedPtr<RenderCommand>& b)
    {
        if (!a || !b)
            return false;
            
        // 首先按优先级排序
        if (a->priority != b->priority)
            return a->priority > b->priority;
            
        // 然后按排序键排序
        return a->sort_key < b->sort_key;
    }

    // UIBackgroundResourceLoader 实现
    UIBackgroundResourceLoader::UIBackgroundResourceLoader()
        : m_shutdown_requested(false)
        , m_stats{}
    {
    }

    UIBackgroundResourceLoader::~UIBackgroundResourceLoader()
    {
        Shutdown();
    }

    HRESULT UIBackgroundResourceLoader::Initialize(uint32_t worker_thread_count)
    {
        try
        {
            m_shutdown_requested = false;
            m_stats = LoaderStats{};
            m_stats.worker_threads = worker_thread_count;
            
            // 创建工作线程
            m_worker_threads.reserve(worker_thread_count);
            for (uint32_t i = 0; i < worker_thread_count; ++i)
            {
                m_worker_threads.emplace_back(&UIBackgroundResourceLoader::WorkerThreadProc, this);
            }
            
            return S_OK;
        }
        catch (const Exception& ex)
        {
            return ex.GetStatus();
        }
    }

    void UIBackgroundResourceLoader::Shutdown()
    {
        // 请求关闭
        m_shutdown_requested = true;
        m_task_cv.notify_all();
        
        // 等待所有工作线程结束
        for (auto& thread : m_worker_threads)
        {
            if (thread.joinable())
            {
                thread.join();
            }
        }
        
        m_worker_threads.clear();
        
        // 清空任务队列
        std::lock_guard<std::mutex> lock(m_task_mutex);
        while (!m_task_queue.empty())
        {
            m_task_queue.pop();
        }
    }

    void UIBackgroundResourceLoader::WorkerThreadProc()
    {
        while (!m_shutdown_requested)
        {
            LoadTask task;
            bool has_task = false;
            
            // 获取任务
            {
                std::unique_lock<std::mutex> lock(m_task_mutex);
                m_task_cv.wait(lock, [this] { 
                    return m_shutdown_requested || !m_task_queue.empty(); 
                });
                
                if (m_shutdown_requested)
                    break;
                    
                if (!m_task_queue.empty())
                {
                    task = m_task_queue.top();
                    m_task_queue.pop();
                    has_task = true;
                    m_stats.tasks_pending--;
                }
            }
            
            // 执行任务
            if (has_task)
            {
                auto start_time = std::chrono::steady_clock::now();
                
                try
                {
                    if (task.execute_func)
                    {
                        task.execute_func();
                    }
                    
                    m_stats.tasks_completed++;
                }
                catch (...)
                {
                    // 记录错误但继续处理其他任务
                }
                
                auto end_time = std::chrono::steady_clock::now();
                auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
                m_stats.average_load_time = duration;
            }
        }
    }

    // UIUpdateRenderSeparator 实现
    UIUpdateRenderSeparator::UIUpdateRenderSeparator()
        : m_shutdown_requested(false)
        , m_ui_update_ready(false)
        , m_render_ready(false)
        , m_target_update_frequency(60)
        , m_target_render_frequency(60)
        , m_stats{}
    {
    }

    UIUpdateRenderSeparator::~UIUpdateRenderSeparator()
    {
        Shutdown();
    }

    HRESULT UIUpdateRenderSeparator::Initialize()
    {
        try
        {
            m_shutdown_requested = false;
            m_stats = SeparatorStats{};
            
            // 创建更新和渲染线程
            m_update_thread = std::thread(&UIUpdateRenderSeparator::UpdateThreadProc, this);
            m_render_thread = std::thread(&UIUpdateRenderSeparator::RenderThreadProc, this);
            
            return S_OK;
        }
        catch (const Exception& ex)
        {
            return ex.GetStatus();
        }
    }

    void UIUpdateRenderSeparator::Shutdown()
    {
        m_shutdown_requested = true;
        m_sync_cv.notify_all();
        
        if (m_update_thread.joinable())
        {
            m_update_thread.join();
        }
        
        if (m_render_thread.joinable())
        {
            m_render_thread.join();
        }
    }

    void UIUpdateRenderSeparator::BeginUIUpdate()
    {
        m_ui_update_ready = true;
    }

    void UIUpdateRenderSeparator::EndUIUpdate()
    {
        m_ui_update_ready = false;
    }

    void UIUpdateRenderSeparator::BeginRender()
    {
        m_render_ready = true;
    }

    void UIUpdateRenderSeparator::EndRender()
    {
        m_render_ready = false;
    }

    void UIUpdateRenderSeparator::SynchronizeUpdateRender()
    {
        std::unique_lock<std::mutex> lock(m_sync_mutex);
        m_sync_cv.wait(lock, [this] { 
            return m_shutdown_requested || (m_ui_update_ready && m_render_ready); 
        });
    }

    void UIUpdateRenderSeparator::SetUpdateFrequency(uint32_t updates_per_second)
    {
        m_target_update_frequency = updates_per_second;
    }

    void UIUpdateRenderSeparator::SetRenderFrequency(uint32_t frames_per_second)
    {
        m_target_render_frequency = frames_per_second;
    }

    void UIUpdateRenderSeparator::UpdateThreadProc()
    {
        auto target_frame_time = std::chrono::microseconds(1000000 / m_target_update_frequency);
        auto last_update_time = std::chrono::steady_clock::now();
        
        while (!m_shutdown_requested)
        {
            auto current_time = std::chrono::steady_clock::now();
            auto elapsed = current_time - last_update_time;
            
            if (elapsed >= target_frame_time)
            {
                auto start_time = std::chrono::steady_clock::now();
                
                // 执行UI更新
                BeginUIUpdate();
                // 这里应该调用实际的UI更新逻辑
                EndUIUpdate();
                
                auto end_time = std::chrono::steady_clock::now();
                auto update_duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
                m_stats.average_update_time = update_duration;
                
                last_update_time = current_time;
                m_stats.ui_updates_per_second++;
                
                UpdateStatistics();
            }
            
            // 短暂休眠以避免过度占用CPU
            std::this_thread::sleep_for(std::chrono::microseconds(100));
        }
    }

    void UIUpdateRenderSeparator::RenderThreadProc()
    {
        auto target_frame_time = std::chrono::microseconds(1000000 / m_target_render_frequency);
        auto last_render_time = std::chrono::steady_clock::now();
        
        while (!m_shutdown_requested)
        {
            auto current_time = std::chrono::steady_clock::now();
            auto elapsed = current_time - last_render_time;
            
            if (elapsed >= target_frame_time)
            {
                auto start_time = std::chrono::steady_clock::now();
                
                // 执行渲染
                BeginRender();
                // 这里应该调用实际的渲染逻辑
                EndRender();
                
                auto end_time = std::chrono::steady_clock::now();
                auto render_duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
                m_stats.average_render_time = render_duration;
                
                last_render_time = current_time;
                m_stats.renders_per_second++;
                
                UpdateStatistics();
            }
            
            // 短暂休眠以避免过度占用CPU
            std::this_thread::sleep_for(std::chrono::microseconds(100));
        }
    }

    void UIUpdateRenderSeparator::UpdateStatistics()
    {
        if (m_stats.renders_per_second > 0)
        {
            m_stats.update_render_ratio = 
                static_cast<float>(m_stats.ui_updates_per_second) / m_stats.renders_per_second;
        }
    }
}
