/**
** =====================================================================================
**
**       文件名称: render_batch.h
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】渲染批处理优化系统 - 高性能Draw Call合并与实例化渲染框架 （声明文件）
**
**       主要功能:
**       - 高性能Draw Call批处理与合并
**       - 实例化渲染支持与管理
**       - 动态顶点缓冲区池管理
**       - 智能渲染状态分组与优化
**       - GPU实例数据流式传输
**       - 渲染命令自动排序与优化
**       - 多线程安全的批处理队列
**
**       技术特性:
**       - 采用现代C++17标准与DirectX11 API
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能批处理算法与数据结构
**       - 多线程安全的渲染队列管理
**       - 自适应批处理策略与优化
**       - 实时性能监控与统计分析
**
**       更新记录:
**       2025-07-31 v1.0.0.0 : 1. 创建渲染批处理优化系统
**                             2. 实现Draw Call合并算法
**                             3. 添加实例化渲染支持
**                             4. 完成动态缓冲区池管理
**                             5. 集成性能监控与统计
**                             6. 确保多线程安全性
**                             7. 实现自适应优化策略
**
** =====================================================================================
**/

#pragma once

// 防止Windows宏干扰
#ifndef NOMINMAX
#define NOMINMAX
#endif

#include "render_api.h"
#include "common/smart_ptr.hpp"
#include "common/enhanced_mem_pool.hpp"
#include <vector>
#include <unordered_map>
#include <queue>
#include <mutex>
#include <atomic>
#include <memory>
#include <DirectXMath.h>
#include <wrl.h>

namespace HHBUI
{
    /// 渲染批处理类型
    enum class BatchType : uint32_t
    {
        STATIC_GEOMETRY = 0,    // 静态几何体
        DYNAMIC_GEOMETRY,       // 动态几何体
        INSTANCED_GEOMETRY,     // 实例化几何体
        UI_ELEMENTS,            // UI元素
        TEXT_RENDERING,         // 文本渲染
        PARTICLE_SYSTEM,        // 粒子系统
        POST_PROCESSING         // 后处理效果
    };

    /// 渲染状态哈希键
    struct RenderStateKey
    {
        uint64_t shader_hash;           // 着色器哈希
        uint64_t texture_hash;          // 纹理哈希
        uint32_t blend_state;           // 混合状态
        uint32_t depth_state;           // 深度状态
        uint32_t raster_state;          // 光栅化状态
        uint32_t topology;              // 图元拓扑
        uint32_t primitive_topology;    // 图元拓扑类型

        // DirectX资源指针
        ExSharedPtr<IShader> vertex_shader;     // 顶点着色器
        ExSharedPtr<IShader> pixel_shader;      // 像素着色器
        ExSharedPtr<IInputLayout> input_layout; // 输入布局

        bool operator==(const RenderStateKey& other) const noexcept
        {
            return shader_hash == other.shader_hash &&
                   texture_hash == other.texture_hash &&
                   blend_state == other.blend_state &&
                   depth_state == other.depth_state &&
                   raster_state == other.raster_state &&
                   topology == other.topology &&
                   primitive_topology == other.primitive_topology &&
                   vertex_shader == other.vertex_shader &&
                   pixel_shader == other.pixel_shader &&
                   input_layout == other.input_layout;
        }
    };

    /// 渲染状态哈希函数
    struct RenderStateKeyHash
    {
        std::size_t operator()(const RenderStateKey& key) const noexcept
        {
            std::size_t h1 = std::hash<uint64_t>{}(key.shader_hash);
            std::size_t h2 = std::hash<uint64_t>{}(key.texture_hash);
            std::size_t h3 = std::hash<uint32_t>{}(key.blend_state);
            std::size_t h4 = std::hash<uint32_t>{}(key.depth_state);
            std::size_t h5 = std::hash<uint32_t>{}(key.raster_state);
            std::size_t h6 = std::hash<uint32_t>{}(key.topology);
            std::size_t h7 = std::hash<uint32_t>{}(key.primitive_topology);

            // 对于智能指针，使用原始指针的哈希值
            std::size_t h8 = std::hash<void*>{}(key.vertex_shader.get());
            std::size_t h9 = std::hash<void*>{}(key.pixel_shader.get());
            std::size_t h10 = std::hash<void*>{}(key.input_layout.get());

            return h1 ^ (h2 << 1) ^ (h3 << 2) ^ (h4 << 3) ^ (h5 << 4) ^ (h6 << 5) ^
                   (h7 << 6) ^ (h8 << 7) ^ (h9 << 8) ^ (h10 << 9);
        }
    };

    /// 实例化数据结构
    struct InstanceData
    {
        DirectX::XMFLOAT4X4 world_matrix;      // 世界变换矩阵
        DirectX::XMFLOAT4 color;               // 实例颜色
        DirectX::XMFLOAT4 uv_transform;        // UV变换参数
        DirectX::XMFLOAT2 scale;               // 缩放因子
        DirectX::XMFLOAT2 rotation;            // 旋转参数
    };

    /// 渲染批次数据
    struct RenderBatch
    {
        RenderStateKey state_key;              // 渲染状态键
        BatchType batch_type;                  // 批次类型
        uint32_t vertex_count;                 // 顶点数量
        uint32_t index_count;                  // 索引数量
        uint32_t instance_count;               // 实例数量
        uint32_t vertex_offset;                // 顶点偏移
        uint32_t index_offset;                 // 索引偏移
        uint32_t instance_offset;              // 实例偏移
        float depth_sort_key;                  // 深度排序键
        uint32_t priority;                     // 渲染优先级
        
        // 资源引用
        ExSharedPtr<IShader> vertex_shader;
        ExSharedPtr<IShader> pixel_shader;
        ExSharedPtr<IBuffer> vertex_buffer;
        ExSharedPtr<IBuffer> index_buffer;
        ExSharedPtr<IBuffer> instance_buffer;
        std::vector<ID3D11ShaderResourceView*> textures;
        
        RenderBatch() : batch_type(BatchType::STATIC_GEOMETRY), vertex_count(0), 
                       index_count(0), instance_count(0), vertex_offset(0), 
                       index_offset(0), instance_offset(0), depth_sort_key(0.0f), 
                       priority(0) {}
    };

    /// 动态顶点缓冲区池
    class UIDynamicBufferPool
    {
    public:
        UIDynamicBufferPool();
        ~UIDynamicBufferPool();

        /// 初始化缓冲区池
        HRESULT Initialize(ID3D11Device* device, uint32_t initial_size = 1024 * 1024);

        /// 关闭缓冲区池
        void Shutdown();

        /// 分配顶点缓冲区空间
        HRESULT AllocateVertexSpace(uint32_t size_bytes, uint32_t& offset, ID3D11Buffer** buffer);

        /// 分配索引缓冲区空间
        HRESULT AllocateIndexSpace(uint32_t size_bytes, uint32_t& offset, ID3D11Buffer** buffer);

        /// 分配实例缓冲区空间
        HRESULT AllocateInstanceSpace(uint32_t size_bytes, uint32_t& offset, ID3D11Buffer** buffer);

        /// 重置缓冲区池（新帧开始时调用）
        void Reset();

        /// 获取统计信息
        struct PoolStats
        {
            uint32_t total_vertex_size;
            uint32_t used_vertex_size;
            uint32_t total_index_size;
            uint32_t used_index_size;
            uint32_t total_instance_size;
            uint32_t used_instance_size;
            uint32_t allocation_count;
            uint32_t reset_count;
        };
        const PoolStats& GetStats() const { return m_stats; }

    private:
        struct BufferInfo
        {
            Microsoft::WRL::ComPtr<ID3D11Buffer> buffer;
            uint32_t size;
            uint32_t used;
            D3D11_MAPPED_SUBRESOURCE mapped_resource;
            bool is_mapped;
        };

        ID3D11Device* m_device;
        ID3D11DeviceContext* m_context;
        
        std::vector<BufferInfo> m_vertex_buffers;
        std::vector<BufferInfo> m_index_buffers;
        std::vector<BufferInfo> m_instance_buffers;
        
        uint32_t m_current_vertex_buffer;
        uint32_t m_current_index_buffer;
        uint32_t m_current_instance_buffer;
        
        PoolStats m_stats;
        std::mutex m_mutex;

        HRESULT CreateBuffer(D3D11_BIND_FLAG bind_flag, uint32_t size, BufferInfo& buffer_info);
        HRESULT AllocateFromPool(std::vector<BufferInfo>& pool, uint32_t& current_index,
                                D3D11_BIND_FLAG bind_flag, uint32_t size_bytes,
                                uint32_t& offset, ID3D11Buffer** buffer);
    };

    /// 渲染批处理管理器
    class UIRenderBatchManager
    {
    public:
        UIRenderBatchManager();
        ~UIRenderBatchManager();

        /// 初始化批处理管理器
        HRESULT Initialize(ID3D11Device* device, ID3D11DeviceContext* context);

        /// 关闭批处理管理器
        void Shutdown();

        /// 开始新的批处理帧
        void BeginFrame();

        /// 结束当前批处理帧
        void EndFrame();

        /// 添加渲染批次
        HRESULT AddBatch(const RenderBatch& batch);

        /// 添加实例化渲染批次
        HRESULT AddInstancedBatch(const RenderStateKey& state_key,
                                 const std::vector<InstanceData>& instances,
                                 ExSharedPtr<IBuffer> vertex_buffer,
                                 ExSharedPtr<IBuffer> index_buffer,
                                 uint32_t vertex_count, uint32_t index_count);

        /// 执行所有批次渲染
        HRESULT ExecuteBatches();

        /// 清空所有批次
        void ClearBatches();

        /// 设置批处理策略
        void SetBatchingStrategy(bool enable_instancing, bool enable_sorting,
                               uint32_t max_instances_per_batch = 1000);

        /// 获取批处理统计信息
        struct BatchStats
        {
            uint32_t total_batches;
            uint32_t merged_batches;
            uint32_t instanced_batches;
            uint32_t total_draw_calls;
            uint32_t saved_draw_calls;
            uint32_t total_instances;
            uint32_t frames_processed;      // 添加frames_processed成员
            uint32_t batches_added;         // 添加batches_added成员
            uint32_t batches_executed;      // 添加batches_executed成员
            uint32_t instances_added;       // 添加instances_added成员
            uint32_t draw_calls;            // 添加draw_calls成员
            uint32_t triangles_rendered;    // 添加triangles_rendered成员
            float batch_efficiency;
        };
        const BatchStats& GetStats() const { return m_stats; }

        /// 获取动态缓冲区池
        UIDynamicBufferPool* GetBufferPool() { return &m_buffer_pool; }

    private:
        ID3D11Device* m_device;
        ID3D11DeviceContext* m_context;

        // 批次存储
        std::unordered_map<RenderStateKey, std::vector<RenderBatch>, RenderStateKeyHash> m_batches;
        std::vector<RenderBatch> m_sorted_batches;

        // 缓冲区池
        UIDynamicBufferPool m_buffer_pool;

        // 批处理策略
        bool m_enable_instancing;
        bool m_enable_sorting;
        uint32_t m_max_instances_per_batch;

        // 统计信息
        BatchStats m_stats;
        std::mutex m_mutex;

        // 内部方法
        void SortBatches();
        HRESULT MergeBatches();
        HRESULT CreateInstancedBatch(const RenderStateKey& state_key,
                                   const std::vector<RenderBatch>& batches);
        HRESULT ExecuteBatch(const RenderBatch& batch);
        uint64_t CalculateStateHash(const RenderStateKey& key) const;
        bool CanMergeBatches(const RenderBatch& a, const RenderBatch& b) const;
    };

    /// 全局批处理管理器实例
    extern UIRenderBatchManager* g_batch_manager;

    /// 便捷宏定义
    #define RENDER_BATCH_BEGIN_FRAME() if(g_batch_manager) g_batch_manager->BeginFrame()
    #define RENDER_BATCH_END_FRAME() if(g_batch_manager) g_batch_manager->EndFrame()
    #define RENDER_BATCH_EXECUTE() if(g_batch_manager) g_batch_manager->ExecuteBatches()
    #define RENDER_BATCH_ADD(batch) if(g_batch_manager) g_batch_manager->AddBatch(batch)

    /// RAII批处理帧管理类
    class ScopedBatchFrame
    {
    public:
        ScopedBatchFrame()
        {
            RENDER_BATCH_BEGIN_FRAME();
        }

        ~ScopedBatchFrame()
        {
            RENDER_BATCH_EXECUTE();
            RENDER_BATCH_END_FRAME();
        }
    };

    #define RENDER_BATCH_SCOPED_FRAME() ScopedBatchFrame _scoped_batch_frame
}
