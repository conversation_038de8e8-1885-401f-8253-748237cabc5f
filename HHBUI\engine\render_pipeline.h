/**
** =====================================================================================
**
**       文件名称: render_pipeline.h
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】可配置渲染管线系统 - 现代化渲染管线架构框架 （声明文件）
**
**       主要功能:
**       - 可配置的渲染管线架构
**       - 延迟渲染支持与管理
**       - 混合渲染模式优化切换
**       - 渲染阶段自动调度
**       - 渲染状态智能缓存
**       - 多渲染目标管理
**       - 渲染管线性能监控
**
**       技术特性:
**       - 采用现代C++17标准与DirectX11 API
**       - 模块化渲染阶段设计
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能渲染调度算法
**       - 自适应渲染策略
**       - 实时性能监控与统计分析
**
**       更新记录:
**       2025-07-31 v1.0.0.0 : 1. 创建可配置渲染管线系统
**                             2. 实现延迟渲染支持
**                             3. 添加混合渲染模式
**                             4. 完成渲染阶段调度
**                             5. 集成状态缓存机制
**                             6. 实现多渲染目标管理
**                             7. 确保性能监控优化
**
** =====================================================================================
**/

#pragma once
#include "render_api.h"
#include "render_batch.h"
#include "antialiasing.h"
#include "post_processing.h"
#include "common/smart_ptr.hpp"
#include <vector>
#include <unordered_map>
#include <functional>
#include <memory>
#include <DirectXMath.h>
#include <wrl.h>

namespace HHBUI
{
    /// 渲染管线类型
    enum class RenderPipelineType : uint32_t
    {
        FORWARD = 0,                // 前向渲染
        DEFERRED,                   // 延迟渲染
        FORWARD_PLUS,               // Forward+渲染
        HYBRID,                     // 混合渲染
        TILE_BASED,                 // 基于瓦片的渲染
        CLUSTERED                   // 聚类渲染
    };

    /// 渲染阶段类型
    enum class RenderStage : uint32_t
    {
        SHADOW_MAP = 0,             // 阴影映射
        DEPTH_PREPASS,              // 深度预通道
        GBUFFER_FILL,               // G-Buffer填充
        LIGHTING,                   // 光照计算
        TRANSPARENCY,               // 透明物体渲染
        POST_PROCESSING,            // 后处理
        UI_OVERLAY,                 // UI覆盖层
        DEBUG_VISUALIZATION,        // 调试可视化
        CUSTOM                      // 自定义阶段
    };

    /// 渲染阶段描述符
    struct RenderStageDesc
    {
        RenderStage stage;
        std::string name;
        uint32_t priority;
        bool enabled;
        std::function<HRESULT(ID3D11DeviceContext*)> execute_func;
        std::vector<RenderStage> dependencies;
        
        RenderStageDesc() : stage(RenderStage::CUSTOM), priority(0), enabled(true) {}
    };

    /// G-Buffer管理器
    class UIGBufferManager
    {
    public:
        UIGBufferManager();
        ~UIGBufferManager();

        /// 初始化G-Buffer
        HRESULT Initialize(ID3D11Device* device, uint32_t width, uint32_t height);

        /// 关闭G-Buffer
        void Shutdown();

        /// 调整G-Buffer大小
        HRESULT ResizeGBuffer(uint32_t width, uint32_t height);

        /// 开始G-Buffer填充
        HRESULT BeginGBufferFill(ID3D11DeviceContext* context);

        /// 结束G-Buffer填充
        HRESULT EndGBufferFill(ID3D11DeviceContext* context);

        /// 绑定G-Buffer用于读取
        HRESULT BindGBufferForReading(ID3D11DeviceContext* context, uint32_t start_slot = 0);

        /// 清空G-Buffer
        void ClearGBuffer(ID3D11DeviceContext* context);

        /// 获取G-Buffer纹理
        ID3D11ShaderResourceView* GetAlbedoSRV() const { return m_albedo_srv.Get(); }
        ID3D11ShaderResourceView* GetNormalSRV() const { return m_normal_srv.Get(); }
        ID3D11ShaderResourceView* GetMaterialSRV() const { return m_material_srv.Get(); }
        ID3D11ShaderResourceView* GetDepthSRV() const { return m_depth_srv.Get(); }

        /// 获取G-Buffer渲染目标视图
        ID3D11RenderTargetView* const* GetRTVs() const { return m_rtvs; }
        uint32_t GetRTVCount() const { return 3; }

        /// 获取深度模板视图
        ID3D11DepthStencilView* GetDSV() const { return m_depth_dsv.Get(); }

    private:
        // G-Buffer纹理 (Albedo + Metallic, Normal + Roughness, Material Properties)
        Microsoft::WRL::ComPtr<ID3D11Texture2D> m_albedo_texture;
        Microsoft::WRL::ComPtr<ID3D11Texture2D> m_normal_texture;
        Microsoft::WRL::ComPtr<ID3D11Texture2D> m_material_texture;
        Microsoft::WRL::ComPtr<ID3D11Texture2D> m_depth_texture;
        
        // 渲染目标视图
        Microsoft::WRL::ComPtr<ID3D11RenderTargetView> m_albedo_rtv;
        Microsoft::WRL::ComPtr<ID3D11RenderTargetView> m_normal_rtv;
        Microsoft::WRL::ComPtr<ID3D11RenderTargetView> m_material_rtv;
        Microsoft::WRL::ComPtr<ID3D11DepthStencilView> m_depth_dsv;
        
        // 着色器资源视图
        Microsoft::WRL::ComPtr<ID3D11ShaderResourceView> m_albedo_srv;
        Microsoft::WRL::ComPtr<ID3D11ShaderResourceView> m_normal_srv;
        Microsoft::WRL::ComPtr<ID3D11ShaderResourceView> m_material_srv;
        Microsoft::WRL::ComPtr<ID3D11ShaderResourceView> m_depth_srv;
        
        ID3D11RenderTargetView* m_rtvs[3];
        uint32_t m_width;
        uint32_t m_height;

        HRESULT CreateGBufferTextures(ID3D11Device* device);
        HRESULT CreateGBufferViews(ID3D11Device* device);
    };

    /// 光照管理器
    class UILightingManager
    {
    public:
        UILightingManager();
        ~UILightingManager();

        /// 初始化光照管理器
        HRESULT Initialize(ID3D11Device* device, uint32_t width, uint32_t height);

        /// 关闭光照管理器
        void Shutdown();

        /// 执行延迟光照
        HRESULT ExecuteDeferredLighting(ID3D11DeviceContext* context,
                                       UIGBufferManager* gbuffer_manager,
                                       ID3D11RenderTargetView* output_rtv);

        /// 执行前向光照
        HRESULT ExecuteForwardLighting(ID3D11DeviceContext* context,
                                      const std::vector<RenderBatch>& batches);

        /// 添加方向光
        void AddDirectionalLight(const DirectX::XMFLOAT3& direction,
                                const DirectX::XMFLOAT3& color,
                                float intensity = 1.0f);

        /// 添加点光源
        void AddPointLight(const DirectX::XMFLOAT3& position,
                          const DirectX::XMFLOAT3& color,
                          float intensity = 1.0f,
                          float range = 10.0f);

        /// 添加聚光灯
        void AddSpotLight(const DirectX::XMFLOAT3& position,
                         const DirectX::XMFLOAT3& direction,
                         const DirectX::XMFLOAT3& color,
                         float intensity = 1.0f,
                         float range = 10.0f,
                         float inner_cone = 30.0f,
                         float outer_cone = 45.0f);

        /// 清空所有光源
        void ClearLights();

        /// 更新光照数据
        HRESULT UpdateLightingData(ID3D11DeviceContext* context);

    private:
        struct DirectionalLight
        {
            DirectX::XMFLOAT3 direction;
            float intensity;
            DirectX::XMFLOAT3 color;
            float padding;
        };

        struct PointLight
        {
            DirectX::XMFLOAT3 position;
            float intensity;
            DirectX::XMFLOAT3 color;
            float range;
        };

        struct SpotLight
        {
            DirectX::XMFLOAT3 position;
            float intensity;
            DirectX::XMFLOAT3 direction;
            float range;
            DirectX::XMFLOAT3 color;
            float inner_cone;
            float outer_cone;
            DirectX::XMFLOAT3 padding;
        };

        struct LightingConstants
        {
            uint32_t directional_light_count;
            uint32_t point_light_count;
            uint32_t spot_light_count;
            uint32_t padding;
            DirectX::XMFLOAT4 ambient_color;
        };

        std::vector<DirectionalLight> m_directional_lights;
        std::vector<PointLight> m_point_lights;
        std::vector<SpotLight> m_spot_lights;
        
        Microsoft::WRL::ComPtr<ID3D11Buffer> m_lighting_constants_buffer;
        Microsoft::WRL::ComPtr<ID3D11Buffer> m_directional_lights_buffer;
        Microsoft::WRL::ComPtr<ID3D11Buffer> m_point_lights_buffer;
        Microsoft::WRL::ComPtr<ID3D11Buffer> m_spot_lights_buffer;
        
        Microsoft::WRL::ComPtr<ID3D11VertexShader> m_deferred_lighting_vs;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_deferred_lighting_ps;
        Microsoft::WRL::ComPtr<ID3D11SamplerState> m_sampler_state;
        
        LightingConstants m_constants;
        uint32_t m_width;
        uint32_t m_height;

        HRESULT CreateLightingShaders(ID3D11Device* device);
        HRESULT CreateLightingBuffers(ID3D11Device* device);
    };

    /// 渲染状态缓存
    class UIRenderStateCache
    {
    public:
        UIRenderStateCache();
        ~UIRenderStateCache();

        /// 初始化状态缓存
        HRESULT Initialize(ID3D11Device* device);

        /// 关闭状态缓存
        void Shutdown();

        /// 设置混合状态
        HRESULT SetBlendState(ID3D11DeviceContext* context, 
                             const D3D11_BLEND_DESC& desc,
                             const float blend_factor[4] = nullptr,
                             uint32_t sample_mask = 0xffffffff);

        /// 设置深度模板状态
        HRESULT SetDepthStencilState(ID3D11DeviceContext* context,
                                    const D3D11_DEPTH_STENCIL_DESC& desc,
                                    uint32_t stencil_ref = 0);

        /// 设置光栅化状态
        HRESULT SetRasterizerState(ID3D11DeviceContext* context,
                                  const D3D11_RASTERIZER_DESC& desc);

        /// 设置采样器状态
        HRESULT SetSamplerState(ID3D11DeviceContext* context,
                               uint32_t slot,
                               const D3D11_SAMPLER_DESC& desc);

        /// 清空缓存
        void ClearCache();

        /// 获取缓存统计
        struct CacheStats
        {
            uint32_t blend_state_hits;
            uint32_t blend_state_misses;
            uint32_t depth_stencil_hits;
            uint32_t depth_stencil_misses;
            uint32_t rasterizer_hits;
            uint32_t rasterizer_misses;
            uint32_t sampler_hits;
            uint32_t sampler_misses;
            float hit_ratio;
        };
        const CacheStats& GetStats() const { return m_stats; }

    private:
        ID3D11Device* m_device;
        
        std::unordered_map<uint64_t, Microsoft::WRL::ComPtr<ID3D11BlendState>> m_blend_state_cache;
        std::unordered_map<uint64_t, Microsoft::WRL::ComPtr<ID3D11DepthStencilState>> m_depth_stencil_cache;
        std::unordered_map<uint64_t, Microsoft::WRL::ComPtr<ID3D11RasterizerState>> m_rasterizer_cache;
        std::unordered_map<uint64_t, Microsoft::WRL::ComPtr<ID3D11SamplerState>> m_sampler_cache;
        
        CacheStats m_stats;

        uint64_t HashBlendDesc(const D3D11_BLEND_DESC& desc);
        uint64_t HashDepthStencilDesc(const D3D11_DEPTH_STENCIL_DESC& desc);
        uint64_t HashRasterizerDesc(const D3D11_RASTERIZER_DESC& desc);
        uint64_t HashSamplerDesc(const D3D11_SAMPLER_DESC& desc);
    };

    /// 渲染管线管理器
    class UIRenderPipelineManager
    {
    public:
        UIRenderPipelineManager();
        ~UIRenderPipelineManager();

        /// 初始化渲染管线管理器
        HRESULT Initialize(ID3D11Device* device, ID3D11DeviceContext* context,
                          uint32_t width, uint32_t height);

        /// 关闭渲染管线管理器
        void Shutdown();

        /// 设置渲染管线类型
        HRESULT SetPipelineType(RenderPipelineType type);

        /// 添加渲染阶段
        void AddRenderStage(const RenderStageDesc& stage_desc);

        /// 移除渲染阶段
        void RemoveRenderStage(RenderStage stage);

        /// 启用/禁用渲染阶段
        void SetStageEnabled(RenderStage stage, bool enabled);

        /// 执行渲染管线
        HRESULT ExecutePipeline(const std::vector<RenderBatch>& batches,
                               ID3D11RenderTargetView* final_rtv);

        /// 调整管线大小
        HRESULT ResizePipeline(uint32_t width, uint32_t height);

        /// 获取G-Buffer管理器
        UIGBufferManager* GetGBufferManager() { return m_gbuffer_manager.get(); }

        /// 获取光照管理器
        UILightingManager* GetLightingManager() { return m_lighting_manager.get(); }

        /// 获取状态缓存
        UIRenderStateCache* GetStateCache() { return m_state_cache.get(); }

        /// 获取管线统计信息
        struct PipelineStats
        {
            RenderPipelineType current_type;
            uint32_t active_stages;
            uint32_t total_stages;
            float total_execution_time_ms;
            std::unordered_map<RenderStage, float> stage_times_ms;
        };
        const PipelineStats& GetStats() const { return m_stats; }

    private:
        ID3D11Device* m_device;
        ID3D11DeviceContext* m_context;

        RenderPipelineType m_current_type;
        std::vector<RenderStageDesc> m_render_stages;

        // 管理器组件
        ExUniquePtr<UIGBufferManager> m_gbuffer_manager;
        ExUniquePtr<UILightingManager> m_lighting_manager;
        ExUniquePtr<UIRenderStateCache> m_state_cache;

        // 中间渲染目标
        Microsoft::WRL::ComPtr<ID3D11Texture2D> m_intermediate_texture;
        Microsoft::WRL::ComPtr<ID3D11RenderTargetView> m_intermediate_rtv;
        Microsoft::WRL::ComPtr<ID3D11ShaderResourceView> m_intermediate_srv;

        uint32_t m_width;
        uint32_t m_height;
        PipelineStats m_stats;

        // 内部方法
        HRESULT ExecuteForwardPipeline(const std::vector<RenderBatch>& batches,
                                      ID3D11RenderTargetView* final_rtv);
        HRESULT ExecuteDeferredPipeline(const std::vector<RenderBatch>& batches,
                                       ID3D11RenderTargetView* final_rtv);
        HRESULT ExecuteHybridPipeline(const std::vector<RenderBatch>& batches,
                                     ID3D11RenderTargetView* final_rtv);

        void SortRenderStages();
        HRESULT ExecuteRenderStage(const RenderStageDesc& stage);
        HRESULT CreateIntermediateResources();
    };

    /// 跨平台渲染抽象层
    class UICrossPlatformRenderer
    {
    public:
        UICrossPlatformRenderer();
        virtual ~UICrossPlatformRenderer();

        /// 渲染后端类型
        enum class RendererType : uint32_t
        {
            DIRECTX11 = 0,
            DIRECTX12,
            VULKAN,
            OPENGL,
            METAL,
            WEBGL
        };

        /// 初始化渲染器
        virtual HRESULT Initialize(void* native_device, uint32_t width, uint32_t height) = 0;

        /// 关闭渲染器
        virtual void Shutdown() = 0;

        /// 开始帧渲染
        virtual HRESULT BeginFrame() = 0;

        /// 结束帧渲染
        virtual HRESULT EndFrame() = 0;

        /// 呈现到屏幕
        virtual HRESULT Present(bool vsync = true) = 0;

        /// 创建缓冲区
        virtual HRESULT CreateBuffer(const void* desc, void** buffer) = 0;

        /// 创建纹理
        virtual HRESULT CreateTexture(const void* desc, void** texture) = 0;

        /// 创建着色器
        virtual HRESULT CreateShader(const void* bytecode, size_t size, void** shader) = 0;

        /// 绘制
        virtual HRESULT Draw(uint32_t vertex_count, uint32_t start_vertex = 0) = 0;

        /// 索引绘制
        virtual HRESULT DrawIndexed(uint32_t index_count, uint32_t start_index = 0, uint32_t base_vertex = 0) = 0;

        /// 获取渲染器类型
        virtual RendererType GetType() const = 0;

        /// 获取原生设备
        virtual void* GetNativeDevice() const = 0;

        /// 获取原生上下文
        virtual void* GetNativeContext() const = 0;
    };

    /// DirectX11渲染器实现
    class UIDx11CrossPlatformRenderer : public UICrossPlatformRenderer
    {
    public:
        UIDx11CrossPlatformRenderer();
        ~UIDx11CrossPlatformRenderer() override;

        HRESULT Initialize(void* native_device, uint32_t width, uint32_t height) override;
        void Shutdown() override;
        HRESULT BeginFrame() override;
        HRESULT EndFrame() override;
        HRESULT Present(bool vsync = true) override;
        HRESULT CreateBuffer(const void* desc, void** buffer) override;
        HRESULT CreateTexture(const void* desc, void** texture) override;
        HRESULT CreateShader(const void* bytecode, size_t size, void** shader) override;
        HRESULT Draw(uint32_t vertex_count, uint32_t start_vertex = 0) override;
        HRESULT DrawIndexed(uint32_t index_count, uint32_t start_index = 0, uint32_t base_vertex = 0) override;
        RendererType GetType() const override { return RendererType::DIRECTX11; }
        void* GetNativeDevice() const override { return m_device.Get(); }
        void* GetNativeContext() const override { return m_context.Get(); }

    private:
        Microsoft::WRL::ComPtr<ID3D11Device> m_device;
        Microsoft::WRL::ComPtr<ID3D11DeviceContext> m_context;
        Microsoft::WRL::ComPtr<IDXGISwapChain> m_swap_chain;
        Microsoft::WRL::ComPtr<ID3D11RenderTargetView> m_back_buffer_rtv;
        uint32_t m_width;
        uint32_t m_height;
    };

    /// 跨平台渲染器工厂
    class UICrossPlatformRendererFactory
    {
    public:
        /// 创建渲染器
        static ExUniquePtr<UICrossPlatformRenderer> CreateRenderer(
            UICrossPlatformRenderer::RendererType type);

        /// 检查渲染器支持
        static bool IsRendererSupported(UICrossPlatformRenderer::RendererType type);

        /// 获取推荐渲染器
        static UICrossPlatformRenderer::RendererType GetRecommendedRenderer();
    };

    /// 全局渲染管线管理器实例
    extern UIRenderPipelineManager* g_render_pipeline_manager;

    /// 便捷宏定义
    #define PIPELINE_SET_TYPE(type) if(g_render_pipeline_manager) g_render_pipeline_manager->SetPipelineType(type)
    #define PIPELINE_EXECUTE(batches, rtv) if(g_render_pipeline_manager) g_render_pipeline_manager->ExecutePipeline(batches, rtv)
    #define PIPELINE_ADD_STAGE(stage) if(g_render_pipeline_manager) g_render_pipeline_manager->AddRenderStage(stage)
}
