/**
** =====================================================================================
**
**       文件名称: resource_manager.hpp
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】现代化资源管理系统 - C++17资源管理框架 （声明文件）
**
**       主要功能:
**       - 高性能资源缓存与管理
**       - 异步资源加载与预加载
**       - 多种缓存策略支持（LRU/LFU/TTL）
**       - 资源依赖关系管理
**       - 内存使用监控与优化
**       - 资源生命周期管理
**       - 线程安全的资源访问
**
**       技术特性:
**       - 采用现代C++17标准与模板设计
**       - 智能指针与RAII资源管理
**       - 异步任务与future/promise机制
**       - 原子操作保证线程安全
**       - 异常安全保证与错误恢复
**       - 可配置的缓存策略
**       - 资源使用统计与分析
**
**       更新记录:
**       2025-07-31 v1.0.0.0 : 1. 创建现代化资源管理系统
**                             2. 实现异步加载机制
**                             3. 添加多种缓存策略
**                             4. 集成资源监控系统
**
** =====================================================================================
*/
#pragma once
#include <memory>
#include <unordered_map>
#include <string>
#include <functional>
#include <future>
#include <atomic>
#include <mutex>
#include <shared_mutex>
#include <chrono>
#include <queue>
#include <list>
#include <thread>
#include <condition_variable>
#include "smart_ptr.hpp"
#include "enhanced_exception.hpp"
#include "memory.h"

namespace HHBUI
{
	// 资源状态
	enum class ResourceState : int
	{
		NotLoaded = 0,      // 未加载
		Loading = 1,        // 加载中
		Loaded = 2,         // 已加载
		Failed = 3,         // 加载失败
		Expired = 4         // 已过期
	};

	// 缓存策略
	enum class CachePolicy : int
	{
		LRU = 0,           // 最近最少使用
		LFU = 1,           // 最不经常使用
		TTL = 2,           // 生存时间
		FIFO = 3,          // 先进先出
		Custom = 4         // 自定义策略
	};

	// 资源优先级
	enum class ResourcePriority : int
	{
		Low = 0,
		Normal = 1,
		High = 2,
		Critical = 3
	};

	// 资源统计信息
	struct ResourceStats
	{
		std::atomic<size_t> total_resources{ 0 };       // 总资源数
		std::atomic<size_t> loaded_resources{ 0 };      // 已加载资源数
		std::atomic<size_t> failed_resources{ 0 };      // 加载失败资源数
		std::atomic<size_t> cache_hits{ 0 };            // 缓存命中数
		std::atomic<size_t> cache_misses{ 0 };          // 缓存未命中数
		std::atomic<size_t> memory_usage{ 0 };          // 内存使用量
		std::atomic<size_t> peak_memory_usage{ 0 };     // 峰值内存使用量
		std::atomic<size_t> load_requests{ 0 };         // 加载请求数
		std::atomic<size_t> async_loads{ 0 };           // 异步加载数
		std::atomic<size_t> evictions{ 0 };             // 驱逐次数
	};

	// 资源配置
	struct ResourceConfig
	{
		size_t max_cache_size = 1024;                           // 最大缓存大小
		size_t max_memory_usage = 256 * 1024 * 1024;           // 最大内存使用量（256MB）
		CachePolicy cache_policy = CachePolicy::LRU;           // 缓存策略
		std::chrono::seconds default_ttl{ 3600 };              // 默认TTL（1小时）
		bool enable_async_loading = true;                      // 启用异步加载
		bool enable_preloading = true;                         // 启用预加载
		size_t async_thread_count = 4;                         // 异步线程数
		size_t preload_queue_size = 100;                       // 预加载队列大小
		bool enable_compression = false;                       // 启用压缩
		bool enable_statistics = true;                         // 启用统计
	};

	// 前向声明
	template<typename T> class ResourceManager;

	/**
	 * @brief 资源条目
	 * 管理单个资源的元数据和状态
	 */
	template<typename T>
	class ResourceEntry
	{
	public:
		using ResourcePtr = ExSharedPtr<T>;
		using LoaderFunc = std::function<ResourcePtr(const std::string&)>;

		ResourceEntry(const std::string& key, LoaderFunc loader, ResourcePriority priority = ResourcePriority::Normal)
			: key_(key), loader_(std::move(loader)), priority_(priority)
			, state_(ResourceState::NotLoaded), access_count_(0)
			, last_access_time_(std::chrono::steady_clock::now())
			, creation_time_(std::chrono::steady_clock::now()) {}

		// 获取资源（同步）
		ResourcePtr get_resource()
		{
			std::unique_lock<std::shared_mutex> lock(mutex_);
			
			update_access_info();
			
			if (state_ == ResourceState::Loaded && resource_)
			{
				return resource_;
			}
			
			if (state_ == ResourceState::Loading)
			{
				// 等待加载完成
				load_cv_.wait(lock, [this] { 
					return state_ != ResourceState::Loading; 
				});
				
				if (state_ == ResourceState::Loaded && resource_)
				{
					return resource_;
				}
			}
			
			// 需要加载资源
			return load_resource_sync(lock);
		}

		// 获取资源（异步）
		std::future<ResourcePtr> get_resource_async()
		{
			std::unique_lock<std::shared_mutex> lock(mutex_);
			
			update_access_info();
			
			if (state_ == ResourceState::Loaded && resource_)
			{
				std::promise<ResourcePtr> promise;
				promise.set_value(resource_);
				return promise.get_future();
			}
			
			if (state_ == ResourceState::Loading && load_future_.valid())
			{
				return load_future_;
			}
			
			// 开始异步加载
			return start_async_load(lock);
		}

		// 检查是否已加载
		bool is_loaded() const
		{
			std::shared_lock<std::shared_mutex> lock(mutex_);
			return state_ == ResourceState::Loaded && resource_ != nullptr;
		}

		// 获取状态
		ResourceState get_state() const
		{
			std::shared_lock<std::shared_mutex> lock(mutex_);
			return state_;
		}

		// 获取访问统计
		size_t get_access_count() const
		{
			std::shared_lock<std::shared_mutex> lock(mutex_);
			return access_count_;
		}

		std::chrono::steady_clock::time_point get_last_access_time() const
		{
			std::shared_lock<std::shared_mutex> lock(mutex_);
			return last_access_time_;
		}

		std::chrono::steady_clock::time_point get_creation_time() const
		{
			std::shared_lock<std::shared_mutex> lock(mutex_);
			return creation_time_;
		}

		ResourcePriority get_priority() const noexcept
		{
			return priority_;
		}

		const std::string& get_key() const noexcept
		{
			return key_;
		}

		// 计算内存使用量
		size_t get_memory_usage() const
		{
			std::shared_lock<std::shared_mutex> lock(mutex_);
			if (resource_)
			{
				// 这里应该根据具体资源类型计算内存使用量
				// 为简化，返回固定值
				return sizeof(T);
			}
			return 0;
		}

		// 释放资源
		void release()
		{
			std::unique_lock<std::shared_mutex> lock(mutex_);
			resource_.reset();
			state_ = ResourceState::NotLoaded;
		}

		// 检查是否过期（用于TTL策略）
		bool is_expired(std::chrono::seconds ttl) const
		{
			std::shared_lock<std::shared_mutex> lock(mutex_);
			auto now = std::chrono::steady_clock::now();
			return (now - last_access_time_) > ttl;
		}

	private:
		std::string key_;
		LoaderFunc loader_;
		ResourcePriority priority_;
		
		mutable std::shared_mutex mutex_;
		ResourceState state_;
		ResourcePtr resource_;
		std::future<ResourcePtr> load_future_;
		std::condition_variable_any load_cv_;
		
		// 访问统计
		size_t access_count_;
		std::chrono::steady_clock::time_point last_access_time_;
		std::chrono::steady_clock::time_point creation_time_;

		void update_access_info()
		{
			access_count_++;
			last_access_time_ = std::chrono::steady_clock::now();
		}

		ResourcePtr load_resource_sync(std::unique_lock<std::shared_mutex>& lock)
		{
			try
			{
				state_ = ResourceState::Loading;
				lock.unlock();
				
				ResourcePtr resource = loader_(key_);
				
				lock.lock();
				resource_ = resource;
				state_ = ResourceState::Loaded;
				load_cv_.notify_all();
				
				return resource;
			}
			catch (...)
			{
				lock.lock();
				state_ = ResourceState::Failed;
				load_cv_.notify_all();
				throw;
			}
		}

		std::future<ResourcePtr> start_async_load(std::unique_lock<std::shared_mutex>& lock)
		{
			state_ = ResourceState::Loading;
			
			auto promise = std::make_shared<std::promise<ResourcePtr>>();
			load_future_ = promise->get_future();
			
			// 在线程池中执行加载
			std::thread([this, promise]() {
				try
				{
					ResourcePtr resource = loader_(key_);
					
					{
						std::unique_lock<std::shared_mutex> lock(mutex_);
						resource_ = resource;
						state_ = ResourceState::Loaded;
						load_cv_.notify_all();
					}
					
					promise->set_value(resource);
				}
				catch (...)
				{
					{
						std::unique_lock<std::shared_mutex> lock(mutex_);
						state_ = ResourceState::Failed;
						load_cv_.notify_all();
					}
					
					promise->set_exception(std::current_exception());
				}
			}).detach();
			
			return load_future_;
		}
	};

	/**
	 * @brief 缓存策略接口
	 */
	template<typename T>
	class CacheStrategy
	{
	public:
		using EntryPtr = ExSharedPtr<ResourceEntry<T>>;

		virtual ~CacheStrategy() = default;

		// 访问资源时调用
		virtual void on_access(const std::string& key, EntryPtr entry) = 0;

		// 添加资源时调用
		virtual void on_add(const std::string& key, EntryPtr entry) = 0;

		// 移除资源时调用
		virtual void on_remove(const std::string& key) = 0;

		// 选择要驱逐的资源
		virtual std::vector<std::string> select_for_eviction(size_t count) = 0;

		// 清空策略状态
		virtual void clear() = 0;
	};

	/**
	 * @brief LRU缓存策略
	 */
	template<typename T>
	class LRUCacheStrategy : public CacheStrategy<T>
	{
	public:
		using EntryPtr = typename CacheStrategy<T>::EntryPtr;

		void on_access(const std::string& key, EntryPtr entry) override
		{
			std::lock_guard<std::mutex> lock(mutex_);

			// 移动到链表头部
			auto it = key_to_iter_.find(key);
			if (it != key_to_iter_.end())
			{
				access_order_.erase(it->second);
			}

			access_order_.push_front(key);
			key_to_iter_[key] = access_order_.begin();
		}

		void on_add(const std::string& key, EntryPtr entry) override
		{
			on_access(key, entry);
		}

		void on_remove(const std::string& key) override
		{
			std::lock_guard<std::mutex> lock(mutex_);

			auto it = key_to_iter_.find(key);
			if (it != key_to_iter_.end())
			{
				access_order_.erase(it->second);
				key_to_iter_.erase(it);
			}
		}

		std::vector<std::string> select_for_eviction(size_t count) override
		{
			std::lock_guard<std::mutex> lock(mutex_);

			std::vector<std::string> result;
			auto it = access_order_.rbegin();

			while (it != access_order_.rend() && result.size() < count)
			{
				result.push_back(*it);
				++it;
			}

			return result;
		}

		void clear() override
		{
			std::lock_guard<std::mutex> lock(mutex_);
			access_order_.clear();
			key_to_iter_.clear();
		}

	private:
		std::mutex mutex_;
		std::list<std::string> access_order_;
		std::unordered_map<std::string, std::list<std::string>::iterator> key_to_iter_;
	};

	/**
	 * @brief 资源管理器
	 * 主要的资源管理类
	 */
	template<typename T>
	class ResourceManager
	{
	public:
		using ResourcePtr = ExSharedPtr<T>;
		using EntryPtr = ExSharedPtr<ResourceEntry<T>>;
		using LoaderFunc = std::function<ResourcePtr(const std::string&)>;
		using CacheStrategyPtr = ExUniquePtr<CacheStrategy<T>>;

		explicit ResourceManager(const ResourceConfig& config = ResourceConfig{})
			: config_(config), shutdown_(false)
		{
			// 创建缓存策略
			switch (config_.cache_policy)
			{
			case CachePolicy::LRU:
				cache_strategy_ = ExMakeUnique<LRUCacheStrategy<T>>();
				break;
			default:
				cache_strategy_ = ExMakeUnique<LRUCacheStrategy<T>>();
				break;
			}

			// 启动后台线程
			if (config_.enable_async_loading)
			{
				for (size_t i = 0; i < config_.async_thread_count; ++i)
				{
					worker_threads_.emplace_back(&ResourceManager::worker_thread, this);
				}
			}

			// 启动清理线程
			cleanup_thread_ = std::thread(&ResourceManager::cleanup_worker, this);
		}

		~ResourceManager()
		{
			shutdown();
		}

		// 禁用拷贝和移动
		ResourceManager(const ResourceManager&) = delete;
		ResourceManager& operator=(const ResourceManager&) = delete;
		ResourceManager(ResourceManager&&) = delete;
		ResourceManager& operator=(ResourceManager&&) = delete;

		/**
		 * @brief 注册资源加载器
		 */
		void register_loader(const std::string& key, LoaderFunc loader,
			ResourcePriority priority = ResourcePriority::Normal)
		{
			std::unique_lock<std::shared_mutex> lock(mutex_);

			auto entry = ExMakeShared<ResourceEntry<T>>(key, std::move(loader), priority);
			entries_[key] = entry;

			if (cache_strategy_)
			{
				cache_strategy_->on_add(key, entry);
			}
		}

		/**
		 * @brief 获取资源（同步）
		 */
		ResourcePtr get_resource(const std::string& key)
		{
			if (config_.enable_statistics)
			{
				stats_.load_requests.fetch_add(1, std::memory_order_relaxed);
			}

			std::shared_lock<std::shared_mutex> lock(mutex_);

			auto it = entries_.find(key);
			if (it == entries_.end())
			{
				if (config_.enable_statistics)
				{
					stats_.cache_misses.fetch_add(1, std::memory_order_relaxed);
				}
				throw_enhanced(E_INVALIDARG, L"资源未注册: " + std::wstring(key.begin(), key.end()),
					ExceptionSeverity::Error, ExceptionCategory::Logic);
			}

			auto entry = it->second;
			lock.unlock();

			// 更新缓存策略
			if (cache_strategy_)
			{
				cache_strategy_->on_access(key, entry);
			}

			auto resource = entry->get_resource();

			if (config_.enable_statistics)
			{
				if (resource)
				{
					stats_.cache_hits.fetch_add(1, std::memory_order_relaxed);
				}
				else
				{
					stats_.cache_misses.fetch_add(1, std::memory_order_relaxed);
				}
			}

			return resource;
		}

		/**
		 * @brief 获取资源（异步）
		 */
		std::future<ResourcePtr> get_resource_async(const std::string& key)
		{
			if (config_.enable_statistics)
			{
				stats_.load_requests.fetch_add(1, std::memory_order_relaxed);
				stats_.async_loads.fetch_add(1, std::memory_order_relaxed);
			}

			std::shared_lock<std::shared_mutex> lock(mutex_);

			auto it = entries_.find(key);
			if (it == entries_.end())
			{
				std::promise<ResourcePtr> promise;
				promise.set_exception(std::make_exception_ptr(
					EnhancedException(E_INVALIDARG, L"资源未注册: " + std::wstring(key.begin(), key.end()),
						ExceptionSeverity::Error, ExceptionCategory::Logic)));
				return promise.get_future();
			}

			auto entry = it->second;
			lock.unlock();

			// 更新缓存策略
			if (cache_strategy_)
			{
				cache_strategy_->on_access(key, entry);
			}

			return entry->get_resource_async();
		}

		/**
		 * @brief 预加载资源
		 */
		void preload_resource(const std::string& key, ResourcePriority priority = ResourcePriority::Normal)
		{
			if (!config_.enable_preloading) return;

			std::unique_lock<std::mutex> lock(preload_mutex_);

			if (preload_queue_.size() < config_.preload_queue_size)
			{
				preload_queue_.emplace(key, priority);
				preload_cv_.notify_one();
			}
		}

		/**
		 * @brief 释放资源
		 */
		void release_resource(const std::string& key)
		{
			std::shared_lock<std::shared_mutex> lock(mutex_);

			auto it = entries_.find(key);
			if (it != entries_.end())
			{
				it->second->release();

				if (cache_strategy_)
				{
					cache_strategy_->on_remove(key);
				}
			}
		}

		/**
		 * @brief 清空所有资源
		 */
		void clear()
		{
			std::unique_lock<std::shared_mutex> lock(mutex_);

			for (auto& [key, entry] : entries_)
			{
				entry->release();
			}

			if (cache_strategy_)
			{
				cache_strategy_->clear();
			}

			// 重置统计
			if (config_.enable_statistics)
			{
				stats_.loaded_resources.store(0, std::memory_order_relaxed);
				stats_.memory_usage.store(0, std::memory_order_relaxed);
			}
		}

		/**
		 * @brief 获取统计信息
		 */
		const ResourceStats& get_stats() const noexcept
		{
			return stats_;
		}

		/**
		 * @brief 获取配置信息
		 */
		const ResourceConfig& get_config() const noexcept
		{
			return config_;
		}

		/**
		 * @brief 关闭管理器
		 */
		void shutdown()
		{
			shutdown_.store(true, std::memory_order_release);

			// 通知所有工作线程
			preload_cv_.notify_all();
			cleanup_cv_.notify_all();

			// 等待线程结束
			for (auto& thread : worker_threads_)
			{
				if (thread.joinable())
				{
					thread.join();
				}
			}

			if (cleanup_thread_.joinable())
			{
				cleanup_thread_.join();
			}

			clear();
		}

	private:
		ResourceConfig config_;
		ResourceStats stats_;
		std::atomic<bool> shutdown_;

		// 资源存储
		mutable std::shared_mutex mutex_;
		std::unordered_map<std::string, EntryPtr> entries_;
		CacheStrategyPtr cache_strategy_;

		// 异步加载
		std::vector<std::thread> worker_threads_;

		// 预加载
		std::mutex preload_mutex_;
		std::condition_variable preload_cv_;
		std::queue<std::pair<std::string, ResourcePriority>> preload_queue_;

		// 清理线程
		std::thread cleanup_thread_;
		std::mutex cleanup_mutex_;
		std::condition_variable cleanup_cv_;

		/**
		 * @brief 工作线程函数
		 */
		void worker_thread()
		{
			while (!shutdown_.load(std::memory_order_acquire))
			{
				std::unique_lock<std::mutex> lock(preload_mutex_);
				preload_cv_.wait(lock, [this] {
					return shutdown_.load(std::memory_order_acquire) || !preload_queue_.empty();
				});

				if (shutdown_.load(std::memory_order_acquire))
				{
					break;
				}

				if (!preload_queue_.empty())
				{
					auto [key, priority] = preload_queue_.front();
					preload_queue_.pop();
					lock.unlock();

					try
					{
						get_resource_async(key);
					}
					catch (...)
					{
						// 忽略预加载错误
					}
				}
			}
		}

		/**
		 * @brief 清理工作线程
		 */
		void cleanup_worker()
		{
			while (!shutdown_.load(std::memory_order_acquire))
			{
				std::unique_lock<std::mutex> lock(cleanup_mutex_);
				cleanup_cv_.wait_for(lock, std::chrono::minutes(1), [this] {
					return shutdown_.load(std::memory_order_acquire);
				});

				if (!shutdown_.load(std::memory_order_acquire))
				{
					lock.unlock();
					perform_cleanup();
				}
			}
		}

		/**
		 * @brief 执行清理操作
		 */
		void perform_cleanup()
		{
			// 检查内存使用量
			size_t current_memory = calculate_memory_usage();

			if (current_memory > config_.max_memory_usage)
			{
				// 需要驱逐一些资源
				size_t target_memory = config_.max_memory_usage * 0.8; // 驱逐到80%
				size_t to_free = current_memory - target_memory;

				evict_resources(to_free);
			}

			// TTL清理
			if (config_.cache_policy == CachePolicy::TTL)
			{
				cleanup_expired_resources();
			}
		}

		/**
		 * @brief 计算内存使用量
		 */
		size_t calculate_memory_usage()
		{
			std::shared_lock<std::shared_mutex> lock(mutex_);

			size_t total = 0;
			for (const auto& [key, entry] : entries_)
			{
				total += entry->get_memory_usage();
			}

			if (config_.enable_statistics)
			{
				stats_.memory_usage.store(total, std::memory_order_relaxed);

				size_t peak = stats_.peak_memory_usage.load(std::memory_order_relaxed);
				while (total > peak && !stats_.peak_memory_usage.compare_exchange_weak(peak, total, std::memory_order_relaxed)) {}
			}

			return total;
		}

		/**
		 * @brief 驱逐资源
		 */
		void evict_resources(size_t target_bytes)
		{
			if (!cache_strategy_) return;

			// 获取要驱逐的资源列表
			auto candidates = cache_strategy_->select_for_eviction(entries_.size());

			size_t freed = 0;
			for (const auto& key : candidates)
			{
				if (freed >= target_bytes) break;

				std::shared_lock<std::shared_mutex> lock(mutex_);
				auto it = entries_.find(key);
				if (it != entries_.end())
				{
					size_t resource_size = it->second->get_memory_usage();
					it->second->release();
					freed += resource_size;

					if (config_.enable_statistics)
					{
						stats_.evictions.fetch_add(1, std::memory_order_relaxed);
					}
				}
			}
		}

		/**
		 * @brief 清理过期资源
		 */
		void cleanup_expired_resources()
		{
			std::shared_lock<std::shared_mutex> lock(mutex_);

			std::vector<std::string> expired_keys;
			for (const auto& [key, entry] : entries_)
			{
				if (entry->is_expired(config_.default_ttl))
				{
					expired_keys.push_back(key);
				}
			}

			lock.unlock();

			for (const auto& key : expired_keys)
			{
				release_resource(key);
			}
		}
	};

	// 类型别名
	template<typename T>
	using ExResourceManager = ResourceManager<T>;

}
