/*
** 版权所有 （c） 2025 HHBUI ：https://hhbui.com/
** 保留所有权利.
** HHBUI 渲染模块头文件 - 包含渲染引擎组件
*/

#pragma once

#include "hhbui_core.h"

// 基础渲染
#include "engine/animation.h"
#include "engine/renderd2d.h"
#include "engine/render_api.h"

// DirectX11 渲染
#include "engine/dx11_shader.h"
#include "engine/dx11_buffer.h"
#include "engine/dx11_render_manager.h"

// 渲染性能
#include "engine/render_profiler.h"
#include "engine/gdi_plus_integration.h"

// 增强渲染系统
#include "engine/render_batch.h"
#include "engine/texture_manager.h"
#include "engine/render_command_queue.h"
#include "engine/antialiasing.h"
#include "engine/post_processing.h"
#include "engine/render_pipeline.h"
#include "engine/resource_streaming.h"

// 增强功能
#include "engine/enhanced_animation.h"
#include "engine/enhanced_layout.h"
#include "engine/performance_monitor.h"
#include "engine/enhanced_engine_integration.h"
#include "engine/enhanced_engine_macros.h"
