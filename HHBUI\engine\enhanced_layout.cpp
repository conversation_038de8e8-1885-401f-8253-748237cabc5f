/**
** =====================================================================================
**
**       文件名称: enhanced_layout.cpp
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】增强布局系统 - 现代化布局引擎与响应式设计框架 （实现文件）
**
** =====================================================================================
**/

#include "pch.h"
#include "enhanced_layout.h"
#include "common/Exception.h"

namespace HHBUI
{
    // 全局增强布局管理器实例
    UIEnhancedLayoutManager* g_enhanced_layout_manager = nullptr;

    // UIFlexboxLayoutEngine 实现
    UIFlexboxLayoutEngine::UIFlexboxLayoutEngine()
        : m_container_properties{}
    {
    }

    UIFlexboxLayoutEngine::~UIFlexboxLayoutEngine()
    {
        ClearItems();
    }

    void UIFlexboxLayoutEngine::SetContainerProperties(const FlexContainerProperties& properties)
    {
        m_container_properties = properties;
    }

    void UIFlexboxLayoutEngine::SetItemProperties(UIControl* item, const FlexItemProperties& properties)
    {
        if (!item)
            return;

        auto it = m_item_indices.find(item);
        if (it != m_item_indices.end())
        {
            m_items[it->second].properties = properties;
        }
    }

    void UIFlexboxLayoutEngine::AddItem(UIControl* item)
    {
        if (!item)
            return;

        // 检查是否已存在
        if (m_item_indices.find(item) != m_item_indices.end())
            return;

        FlexItem flex_item;
        flex_item.control = item;
        flex_item.properties = FlexItemProperties{}; // 默认属性
        flex_item.is_frozen = false;

        size_t index = m_items.size();
        m_items.push_back(flex_item);
        m_item_indices[item] = index;
    }

    void UIFlexboxLayoutEngine::RemoveItem(UIControl* item)
    {
        if (!item)
            return;

        auto it = m_item_indices.find(item);
        if (it != m_item_indices.end())
        {
            size_t index = it->second;
            
            // 移除项目
            m_items.erase(m_items.begin() + index);
            m_item_indices.erase(it);
            
            // 更新索引
            for (auto& pair : m_item_indices)
            {
                if (pair.second > index)
                {
                    pair.second--;
                }
            }
        }
    }

    void UIFlexboxLayoutEngine::CalculateLayout(const ExRectF& container_rect)
    {
        if (m_items.empty())
            return;

        // 按order属性排序
        SortItemsByOrder();

        // 创建flex行
        std::vector<FlexLine> lines = CreateFlexLines(container_rect);

        // 对齐项目
        AlignItems(lines, container_rect);

        // 对齐内容
        AlignContent(lines, container_rect);
    }

    ExRectF UIFlexboxLayoutEngine::GetItemRect(UIControl* item) const
    {
        auto it = m_item_indices.find(item);
        if (it != m_item_indices.end())
        {
            return m_items[it->second].calculated_rect;
        }
        return ExRectF{};
    }

    void UIFlexboxLayoutEngine::ClearItems()
    {
        m_items.clear();
        m_item_indices.clear();
    }

    void UIFlexboxLayoutEngine::SortItemsByOrder()
    {
        std::sort(m_items.begin(), m_items.end(),
                 [](const FlexItem& a, const FlexItem& b) {
                     return a.properties.order < b.properties.order;
                 });

        // 重建索引映射
        m_item_indices.clear();
        for (size_t i = 0; i < m_items.size(); ++i)
        {
            m_item_indices[m_items[i].control] = i;
        }
    }

    std::vector<UIFlexboxLayoutEngine::FlexLine> UIFlexboxLayoutEngine::CreateFlexLines(const ExRectF& container_rect)
    {
        std::vector<FlexLine> lines;
        FlexLine current_line;

        float available_main_size = GetMainAxisSize(container_rect);
        float current_main_size = 0.0f;

        for (auto& item : m_items)
        {
            // 获取项目的基础大小
            float item_main_size = item.properties.flex_basis >= 0 ? 
                                  item.properties.flex_basis : 
                                  GetMainAxisSize(item.control->GetRect());

            // 检查是否需要换行
            if (m_container_properties.wrap != FlexWrap::NO_WRAP &&
                !current_line.items.empty() &&
                current_main_size + item_main_size > available_main_size)
            {
                // 完成当前行
                current_line.main_size = current_main_size;
                current_line.remaining_space = available_main_size - current_main_size;
                lines.push_back(current_line);

                // 开始新行
                current_line = FlexLine{};
                current_main_size = 0.0f;
            }

            // 添加项目到当前行
            item.main_size = item_main_size;
            current_line.items.push_back(&item);
            current_main_size += item_main_size + m_container_properties.gap;
        }

        // 添加最后一行
        if (!current_line.items.empty())
        {
            current_line.main_size = current_main_size;
            current_line.remaining_space = available_main_size - current_main_size;
            lines.push_back(current_line);
        }

        // 解析弹性长度
        for (auto& line : lines)
        {
            ResolveFlexibleLengths(line, available_main_size);
        }

        return lines;
    }

    void UIFlexboxLayoutEngine::ResolveFlexibleLengths(FlexLine& line, float available_space)
    {
        if (line.items.empty())
            return;

        float total_flex_grow = 0.0f;
        float total_flex_shrink = 0.0f;

        // 计算总的flex值
        for (auto* item : line.items)
        {
            total_flex_grow += item->properties.flex_grow;
            total_flex_shrink += item->properties.flex_shrink;
        }

        // 分配剩余空间
        if (line.remaining_space > 0 && total_flex_grow > 0)
        {
            // 扩展项目
            for (auto* item : line.items)
            {
                if (item->properties.flex_grow > 0)
                {
                    float grow_amount = (item->properties.flex_grow / total_flex_grow) * line.remaining_space;
                    item->main_size += grow_amount;
                }
            }
        }
        else if (line.remaining_space < 0 && total_flex_shrink > 0)
        {
            // 收缩项目
            float shrink_space = -line.remaining_space;
            for (auto* item : line.items)
            {
                if (item->properties.flex_shrink > 0)
                {
                    float shrink_amount = (item->properties.flex_shrink / total_flex_shrink) * shrink_space;
                    item->main_size = std::max(0.0f, item->main_size - shrink_amount);
                }
            }
        }
    }

    void UIFlexboxLayoutEngine::AlignItems(const std::vector<FlexLine>& lines, const ExRectF& container_rect)
    {
        float main_position = GetMainAxisPosition(container_rect);

        for (const auto& line : lines)
        {
            float cross_position = GetCrossAxisPosition(container_rect);
            float line_main_position = main_position;

            // 主轴对齐
            switch (m_container_properties.justify_content)
            {
            case FlexAlign::FLEX_START:
                // 默认位置
                break;
            case FlexAlign::FLEX_END:
                line_main_position += line.remaining_space;
                break;
            case FlexAlign::CENTER:
                line_main_position += line.remaining_space / 2.0f;
                break;
            case FlexAlign::SPACE_BETWEEN:
                // 在项目之间分配空间
                break;
            case FlexAlign::SPACE_AROUND:
                // 在项目周围分配空间
                break;
            }

            // 设置每个项目的位置
            for (auto* item : line.items)
            {
                ExRectF rect = item->control->GetRect();
                
                // 设置主轴位置和大小
                SetMainAxisPosition(rect, line_main_position);
                SetMainAxisSize(rect, item->main_size);
                
                // 设置交叉轴位置和大小
                SetCrossAxisPosition(rect, cross_position);
                
                // 应用对齐
                FlexAlign align = (item->properties.align_self != FlexAlign::STRETCH) ?
                                 item->properties.align_self : m_container_properties.align_items;
                
                switch (align)
                {
                case FlexAlign::FLEX_START:
                    // 默认位置
                    break;
                case FlexAlign::FLEX_END:
                    SetCrossAxisPosition(rect, cross_position + line.cross_size - GetCrossAxisSize(rect));
                    break;
                case FlexAlign::CENTER:
                    SetCrossAxisPosition(rect, cross_position + (line.cross_size - GetCrossAxisSize(rect)) / 2.0f);
                    break;
                case FlexAlign::STRETCH:
                    SetCrossAxisSize(rect, line.cross_size);
                    break;
                }

                item->calculated_rect = rect;
                item->control->SetRect(rect);
                
                line_main_position += item->main_size + m_container_properties.gap;
            }
        }
    }

    void UIFlexboxLayoutEngine::AlignContent(const std::vector<FlexLine>& lines, const ExRectF& container_rect)
    {
        if (lines.size() <= 1)
            return;

        float total_cross_size = 0.0f;
        for (const auto& line : lines)
        {
            total_cross_size += line.cross_size;
        }

        float available_cross_size = GetCrossAxisSize(container_rect);
        float remaining_space = available_cross_size - total_cross_size;

        float cross_position = GetCrossAxisPosition(container_rect);

        switch (m_container_properties.align_content)
        {
        case FlexAlign::FLEX_START:
            // 默认位置
            break;
        case FlexAlign::FLEX_END:
            cross_position += remaining_space;
            break;
        case FlexAlign::CENTER:
            cross_position += remaining_space / 2.0f;
            break;
        case FlexAlign::SPACE_BETWEEN:
            // 在行之间分配空间
            break;
        case FlexAlign::SPACE_AROUND:
            // 在行周围分配空间
            break;
        }

        // 更新每行的交叉轴位置
        for (const auto& line : lines)
        {
            for (auto* item : line.items)
            {
                ExRectF rect = item->calculated_rect;
                SetCrossAxisPosition(rect, cross_position);
                item->calculated_rect = rect;
                item->control->SetRect(rect);
            }
            cross_position += line.cross_size;
        }
    }

    float UIFlexboxLayoutEngine::GetMainAxisSize(const ExRectF& rect) const
    {
        switch (m_container_properties.direction)
        {
        case FlexDirection::ROW:
        case FlexDirection::ROW_REVERSE:
            return rect.width();
        case FlexDirection::COLUMN:
        case FlexDirection::COLUMN_REVERSE:
            return rect.height();
        default:
            return rect.width();
        }
    }

    float UIFlexboxLayoutEngine::GetCrossAxisSize(const ExRectF& rect) const
    {
        switch (m_container_properties.direction)
        {
        case FlexDirection::ROW:
        case FlexDirection::ROW_REVERSE:
            return rect.height();
        case FlexDirection::COLUMN:
        case FlexDirection::COLUMN_REVERSE:
            return rect.width();
        default:
            return rect.height();
        }
    }

    void UIFlexboxLayoutEngine::SetMainAxisSize(ExRectF& rect, float size) const
    {
        switch (m_container_properties.direction)
        {
        case FlexDirection::ROW:
        case FlexDirection::ROW_REVERSE:
            rect.width(size);
            break;
        case FlexDirection::COLUMN:
        case FlexDirection::COLUMN_REVERSE:
            rect.height(size);
            break;
        }
    }

    void UIFlexboxLayoutEngine::SetCrossAxisSize(ExRectF& rect, float size) const
    {
        switch (m_container_properties.direction)
        {
        case FlexDirection::ROW:
        case FlexDirection::ROW_REVERSE:
            rect.height(size);
            break;
        case FlexDirection::COLUMN:
        case FlexDirection::COLUMN_REVERSE:
            rect.width(size);
            break;
        }
    }

    float UIFlexboxLayoutEngine::GetMainAxisPosition(const ExRectF& rect) const
    {
        switch (m_container_properties.direction)
        {
        case FlexDirection::ROW:
        case FlexDirection::ROW_REVERSE:
            return rect.left;
        case FlexDirection::COLUMN:
        case FlexDirection::COLUMN_REVERSE:
            return rect.top;
        default:
            return rect.left;
        }
    }

    float UIFlexboxLayoutEngine::GetCrossAxisPosition(const ExRectF& rect) const
    {
        switch (m_container_properties.direction)
        {
        case FlexDirection::ROW:
        case FlexDirection::ROW_REVERSE:
            return rect.top;
        case FlexDirection::COLUMN:
        case FlexDirection::COLUMN_REVERSE:
            return rect.left;
        default:
            return rect.top;
        }
    }

    void UIFlexboxLayoutEngine::SetMainAxisPosition(ExRectF& rect, float position) const
    {
        switch (m_container_properties.direction)
        {
        case FlexDirection::ROW:
        case FlexDirection::ROW_REVERSE:
            rect.left = position;
            break;
        case FlexDirection::COLUMN:
        case FlexDirection::COLUMN_REVERSE:
            rect.top = position;
            break;
        }
    }

    void UIFlexboxLayoutEngine::SetCrossAxisPosition(ExRectF& rect, float position) const
    {
        switch (m_container_properties.direction)
        {
        case FlexDirection::ROW:
        case FlexDirection::ROW_REVERSE:
            rect.top = position;
            break;
        case FlexDirection::COLUMN:
        case FlexDirection::COLUMN_REVERSE:
            rect.left = position;
            break;
        }
    }

    // UIResponsiveLayoutManager 实现
    UIResponsiveLayoutManager::UIResponsiveLayoutManager()
        : m_screen_width(0.0f)
        , m_screen_height(0.0f)
        , m_current_breakpoint(ResponsiveBreakpoint::MD)
    {
        // 设置默认断点
        m_breakpoints[ResponsiveBreakpoint::XS] = 0.0f;
        m_breakpoints[ResponsiveBreakpoint::SM] = 576.0f;
        m_breakpoints[ResponsiveBreakpoint::MD] = 768.0f;
        m_breakpoints[ResponsiveBreakpoint::LG] = 992.0f;
        m_breakpoints[ResponsiveBreakpoint::XL] = 1200.0f;
        m_breakpoints[ResponsiveBreakpoint::XXL] = 1400.0f;
    }

    UIResponsiveLayoutManager::~UIResponsiveLayoutManager()
    {
    }

    void UIResponsiveLayoutManager::SetBreakpoints(const std::unordered_map<ResponsiveBreakpoint, float>& breakpoints)
    {
        m_breakpoints = breakpoints;
    }

    void UIResponsiveLayoutManager::SetResponsiveProperties(UIControl* control, const ResponsiveProperties& properties)
    {
        if (control)
        {
            m_responsive_properties[control] = properties;
        }
    }

    void UIResponsiveLayoutManager::UpdateScreenSize(float width, float height)
    {
        m_screen_width = width;
        m_screen_height = height;
        
        ResponsiveBreakpoint new_breakpoint = CalculateBreakpoint(width);
        if (new_breakpoint != m_current_breakpoint)
        {
            m_current_breakpoint = new_breakpoint;
            ApplyResponsiveLayout();
        }
    }

    void UIResponsiveLayoutManager::ApplyResponsiveLayout()
    {
        for (const auto& [control, properties] : m_responsive_properties)
        {
            ApplyPropertiesForBreakpoint(control, m_current_breakpoint);
        }
    }

    ResponsiveBreakpoint UIResponsiveLayoutManager::CalculateBreakpoint(float width) const
    {
        ResponsiveBreakpoint breakpoint = ResponsiveBreakpoint::XS;
        
        for (const auto& [bp, threshold] : m_breakpoints)
        {
            if (width >= threshold)
            {
                breakpoint = bp;
            }
        }
        
        return breakpoint;
    }

    void UIResponsiveLayoutManager::ApplyPropertiesForBreakpoint(UIControl* control, ResponsiveBreakpoint breakpoint)
    {
        auto it = m_responsive_properties.find(control);
        if (it == m_responsive_properties.end())
            return;

        const ResponsiveProperties& props = it->second;

        // 应用尺寸
        auto size_it = props.sizes.find(breakpoint);
        if (size_it != props.sizes.end())
        {
            control->SetRect(size_it->second);
        }

        // 应用可见性
        auto visibility_it = props.visibility.find(breakpoint);
        if (visibility_it != props.visibility.end())
        {
            control->SetVisible(visibility_it->second);
        }

        // 应用flex属性（如果控件在flex容器中）
        auto flex_it = props.flex_properties.find(breakpoint);
        if (flex_it != props.flex_properties.end())
        {
            // 这里需要与flex布局引擎集成
            // 暂时跳过实现
        }
    }

    // UIEnhancedLayoutManager 实现
    UIEnhancedLayoutManager::UIEnhancedLayoutManager()
        : m_auto_update(true)
        , m_stats{}
    {
    }

    UIEnhancedLayoutManager::~UIEnhancedLayoutManager()
    {
        Shutdown();
    }

    HRESULT UIEnhancedLayoutManager::Initialize()
    {
        m_stats = LayoutStats{};
        return S_OK;
    }

    void UIEnhancedLayoutManager::Shutdown()
    {
        m_flexbox_layouts.clear();
        m_responsive_layouts.clear();
        m_constraint_layouts.clear();
        m_stats = LayoutStats{};
    }

    ExSharedPtr<UIFlexboxLayoutEngine> UIEnhancedLayoutManager::CreateFlexboxLayout()
    {
        auto layout = ExMakeShared<UIFlexboxLayoutEngine>();
        m_stats.flexbox_layouts++;
        return layout;
    }

    ExSharedPtr<UIResponsiveLayoutManager> UIEnhancedLayoutManager::CreateResponsiveLayout()
    {
        auto layout = ExMakeShared<UIResponsiveLayoutManager>();
        m_responsive_layouts.push_back(layout);
        m_stats.responsive_layouts++;
        return layout;
    }

    ExSharedPtr<UIConstraintLayoutEngine> UIEnhancedLayoutManager::CreateConstraintLayout()
    {
        auto layout = ExMakeShared<UIConstraintLayoutEngine>();
        m_constraint_layouts.push_back(layout);
        m_stats.constraint_layouts++;
        return layout;
    }

    void UIEnhancedLayoutManager::RegisterLayoutContainer(UIControl* container, ExSharedPtr<UIFlexboxLayoutEngine> layout)
    {
        if (container && layout)
        {
            m_flexbox_layouts[container] = layout;
        }
    }

    void UIEnhancedLayoutManager::UnregisterLayoutContainer(UIControl* container)
    {
        auto it = m_flexbox_layouts.find(container);
        if (it != m_flexbox_layouts.end())
        {
            m_flexbox_layouts.erase(it);
            m_stats.flexbox_layouts--;
        }
    }

    void UIEnhancedLayoutManager::UpdateAllLayouts()
    {
        auto start_time = std::chrono::steady_clock::now();
        
        m_stats.layout_updates_per_frame = 0;

        // 更新所有flexbox布局
        for (const auto& [container, layout] : m_flexbox_layouts)
        {
            if (container && layout)
            {
                layout->CalculateLayout(container->GetRect());
                m_stats.layout_updates_per_frame++;
            }
        }

        // 更新所有响应式布局
        for (auto& layout : m_responsive_layouts)
        {
            if (layout)
            {
                layout->ApplyResponsiveLayout();
                m_stats.layout_updates_per_frame++;
            }
        }

        // 更新所有约束布局
        for (auto& layout : m_constraint_layouts)
        {
            if (layout)
            {
                // 约束布局的更新逻辑
                m_stats.layout_updates_per_frame++;
            }
        }

        auto end_time = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        m_stats.total_calculation_time_ms = duration.count() / 1000.0f;
    }

    void UIEnhancedLayoutManager::CalculateLayoutPerformance()
    {
        // 计算布局性能指标
        // 这里可以添加更详细的性能分析
    }
}
