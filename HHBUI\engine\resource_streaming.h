/**
** =====================================================================================
**
**       文件名称: resource_streaming.h
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】资源流式管理系统 - 智能资源预加载与LOD管理框架 （声明文件）
**
**       主要功能:
**       - 智能资源预加载与卸载
**       - LOD（细节层次）系统管理
**       - 纹理图集自动生成与管理
**       - 资源优先级调度
**       - 内存使用优化
**       - 异步资源加载
**       - 资源生命周期管理
**
**       技术特性:
**       - 采用现代C++17标准与多线程技术
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能资源调度算法
**       - 自适应LOD策略
**       - 多线程安全的资源管理
**       - 实时性能监控与统计分析
**
**       更新记录:
**       2025-07-31 v1.0.0.0 : 1. 创建资源流式管理系统
**                             2. 实现智能资源预加载
**                             3. 添加LOD系统支持
**                             4. 完成纹理图集管理
**                             5. 集成资源优先级调度
**                             6. 实现异步加载机制
**                             7. 确保多线程安全性
**
** =====================================================================================
**/

#pragma once
#include "render_api.h"
#include "texture_manager.h"
#include "common/smart_ptr.hpp"
#include "common/enhanced_mem_pool.hpp"
#include <vector>
#include <unordered_map>
#include <queue>
#include <mutex>
#include <atomic>
#include <thread>
#include <future>
#include <DirectXMath.h>

namespace HHBUI
{
    /// 资源类型
    enum class ResourceType : uint32_t
    {
        TEXTURE = 0,
        MESH,
        SHADER,
        AUDIO,
        FONT,
        ANIMATION,
        MATERIAL,
        CUSTOM
    };

    /// 资源流式优先级（使用不同的名称避免冲突）
    enum class StreamingPriority : uint32_t
    {
        CRITICAL = 0,               // 关键资源（立即加载）
        HIGH,                       // 高优先级（优先加载）
        NORMAL,                     // 普通优先级（正常加载）
        LOW,                        // 低优先级（延迟加载）
        BACKGROUND                  // 后台加载（空闲时加载）
    };

    /// LOD级别
    enum class LODLevel : uint32_t
    {
        LOD_0 = 0,                  // 最高质量
        LOD_1,                      // 高质量
        LOD_2,                      // 中等质量
        LOD_3,                      // 低质量
        LOD_4,                      // 最低质量
        LOD_COUNT
    };

    /// 资源描述符
    struct ResourceDesc
    {
        std::wstring file_path;
        ResourceType type;
        StreamingPriority priority;
        LODLevel lod_level;
        uint64_t estimated_size;
        float distance_from_camera;
        bool is_visible;
        std::chrono::steady_clock::time_point last_access_time;
        
        ResourceDesc() : type(ResourceType::TEXTURE), priority(StreamingPriority::NORMAL),
                        lod_level(LODLevel::LOD_0), estimated_size(0),
                        distance_from_camera(0.0f), is_visible(true),
                        last_access_time(std::chrono::steady_clock::now()) {}
    };

    /// 资源项
    struct ResourceItem
    {
        ResourceDesc desc;
        ExSharedPtr<void> resource_data;
        std::atomic<bool> is_loaded;
        std::atomic<bool> is_loading;
        uint32_t reference_count;

        ResourceItem() : is_loaded(false), is_loading(false), reference_count(0) {}

        // 拷贝构造函数
        ResourceItem(const ResourceItem& other)
            : desc(other.desc)
            , resource_data(other.resource_data)
            , is_loaded(other.is_loaded.load())
            , is_loading(other.is_loading.load())
            , reference_count(other.reference_count) {}

        // 移动构造函数
        ResourceItem(ResourceItem&& other) noexcept
            : desc(std::move(other.desc))
            , resource_data(std::move(other.resource_data))
            , is_loaded(other.is_loaded.load())
            , is_loading(other.is_loading.load())
            , reference_count(other.reference_count) {}

        // 赋值操作符
        ResourceItem& operator=(const ResourceItem& other)
        {
            if (this != &other)
            {
                desc = other.desc;
                resource_data = other.resource_data;
                is_loaded.store(other.is_loaded.load());
                is_loading.store(other.is_loading.load());
                reference_count = other.reference_count;
            }
            return *this;
        }

        // 移动赋值操作符
        ResourceItem& operator=(ResourceItem&& other) noexcept
        {
            if (this != &other)
            {
                desc = std::move(other.desc);
                resource_data = std::move(other.resource_data);
                is_loaded.store(other.is_loaded.load());
                is_loading.store(other.is_loading.load());
                reference_count = other.reference_count;
            }
            return *this;
        }
    };

    /// LOD管理器
    class UILODManager
    {
    public:
        UILODManager();
        ~UILODManager();

        /// 初始化LOD管理器
        HRESULT Initialize();

        /// 关闭LOD管理器
        void Shutdown();

        /// 注册LOD资源
        void RegisterLODResource(const std::wstring& base_path,
                                const std::vector<std::wstring>& lod_paths);

        /// 获取适当的LOD级别
        LODLevel GetAppropriateLevel(float distance, float screen_size, 
                                   ResourceType type) const;

        /// 设置LOD距离阈值
        void SetLODDistanceThresholds(ResourceType type, 
                                     const std::vector<float>& thresholds);

        /// 设置自动LOD调节
        void SetAutoLODAdjustment(bool enable, float target_frametime_ms = 16.67f);

        /// 更新LOD系统
        void UpdateLOD(float current_frametime_ms);

        /// 获取LOD统计信息
        struct LODStats
        {
            std::array<uint32_t, static_cast<size_t>(LODLevel::LOD_COUNT)> resources_per_level;
            uint32_t total_lod_switches;
            float average_lod_level;
            bool auto_adjustment_enabled;
        };
        const LODStats& GetStats() const { return m_stats; }

    private:
        std::unordered_map<ResourceType, std::vector<float>> m_distance_thresholds;
        std::unordered_map<std::wstring, std::vector<std::wstring>> m_lod_resources;
        
        bool m_auto_adjustment_enabled;
        float m_target_frametime_ms;
        std::vector<float> m_frametime_history;
        uint32_t m_frametime_index;
        
        LODStats m_stats;
        mutable std::mutex m_mutex;

        void AdjustLODLevels(float current_frametime_ms);
        float CalculateScreenSize(float distance, float object_size) const;
    };

    /// 纹理图集管理器
    class UITextureAtlasManager
    {
    public:
        UITextureAtlasManager();
        ~UITextureAtlasManager();

        /// 初始化图集管理器
        HRESULT Initialize(ID3D11Device* device);

        /// 关闭图集管理器
        void Shutdown();

        /// 创建纹理图集
        HRESULT CreateAtlas(const std::vector<std::wstring>& texture_paths,
                           uint32_t atlas_width, uint32_t atlas_height,
                           const std::wstring& atlas_name);

        /// 获取纹理在图集中的UV坐标
        bool GetTextureUV(const std::wstring& texture_path,
                         const std::wstring& atlas_name,
                         DirectX::XMFLOAT4& uv_rect);

        /// 获取图集纹理
        ExSharedPtr<UITexture> GetAtlasTexture(const std::wstring& atlas_name);

        /// 优化图集布局
        HRESULT OptimizeAtlasLayout(const std::wstring& atlas_name);

        /// 获取图集统计信息
        struct AtlasStats
        {
            uint32_t total_atlases;
            uint32_t total_textures;
            float average_utilization;
            uint64_t total_memory_saved;
        };
        const AtlasStats& GetStats() const { return m_stats; }

    private:
        struct AtlasEntry
        {
            ExSharedPtr<UITexture> atlas_texture;
            std::unordered_map<std::wstring, DirectX::XMFLOAT4> texture_uvs;
            uint32_t width;
            uint32_t height;
            float utilization;
        };

        struct TextureRect
        {
            uint32_t x, y, width, height;
            std::wstring texture_path;
        };

        ID3D11Device* m_device;
        std::unordered_map<std::wstring, AtlasEntry> m_atlases;
        AtlasStats m_stats;
        std::mutex m_mutex;

        bool PackTextures(const std::vector<TextureRect>& textures,
                         uint32_t atlas_width, uint32_t atlas_height,
                         std::vector<TextureRect>& packed_textures);
        HRESULT CreateAtlasTexture(const std::vector<TextureRect>& packed_textures,
                                  uint32_t atlas_width, uint32_t atlas_height,
                                  ExSharedPtr<UITexture>& atlas_texture);
    };

    /// 资源流式管理器
    class UIResourceStreamingManager
    {
    public:
        UIResourceStreamingManager();
        ~UIResourceStreamingManager();

        /// 初始化流式管理器
        HRESULT Initialize(uint32_t worker_thread_count = 4,
                          uint64_t memory_budget = 512 * 1024 * 1024);

        /// 关闭流式管理器
        void Shutdown();

        /// 注册资源
        void RegisterResource(const ResourceDesc& desc);

        /// 请求资源
        std::future<ExSharedPtr<void>> RequestResource(const std::wstring& file_path,
                                                       StreamingPriority priority = StreamingPriority::NORMAL);

        /// 预加载资源
        void PreloadResource(const std::wstring& file_path,
                            StreamingPriority priority = StreamingPriority::LOW);

        /// 卸载资源
        void UnloadResource(const std::wstring& file_path);

        /// 更新可见性信息
        void UpdateVisibility(const std::wstring& file_path, bool is_visible, float distance);

        /// 执行垃圾回收
        void GarbageCollect();

        /// 设置内存预算
        void SetMemoryBudget(uint64_t budget_bytes);

        /// 获取LOD管理器
        UILODManager* GetLODManager() { return &m_lod_manager; }

        /// 获取图集管理器
        UITextureAtlasManager* GetAtlasManager() { return &m_atlas_manager; }

        /// 获取统计信息
        struct StreamingStats
        {
            uint32_t registered_resources;
            uint32_t loaded_resources;
            uint32_t loading_resources;
            uint64_t memory_usage;
            uint64_t memory_budget;
            uint32_t cache_hits;
            uint32_t cache_misses;
            float memory_utilization;
            float cache_hit_ratio;
        };
        const StreamingStats& GetStats() const { return m_stats; }

    private:
        struct LoadRequest
        {
            std::wstring file_path;
            StreamingPriority priority;
            std::promise<ExSharedPtr<void>> promise;
            std::chrono::steady_clock::time_point request_time;

            // 禁用拷贝构造函数和拷贝赋值操作符
            LoadRequest(const LoadRequest&) = delete;
            LoadRequest& operator=(const LoadRequest&) = delete;

            // 启用移动构造函数和移动赋值操作符
            LoadRequest(LoadRequest&&) = default;
            LoadRequest& operator=(LoadRequest&&) = default;

            // 默认构造函数
            LoadRequest() = default;

            // 带参数的构造函数
            LoadRequest(const std::wstring& path, StreamingPriority prio)
                : file_path(path), priority(prio), request_time(std::chrono::steady_clock::now())
            {
            }

            bool operator<(const LoadRequest& other) const
            {
                return priority > other.priority; // 高优先级在前
            }
        };

        std::unordered_map<std::wstring, ResourceItem> m_resources;
        std::priority_queue<LoadRequest> m_load_queue;
        std::vector<std::thread> m_worker_threads;
        
        UILODManager m_lod_manager;
        UITextureAtlasManager m_atlas_manager;
        
        uint64_t m_memory_budget;
        uint64_t m_current_memory_usage;
        
        std::atomic<bool> m_shutdown_requested;
        StreamingStats m_stats;
        
        std::mutex m_resources_mutex;
        std::mutex m_queue_mutex;
        std::condition_variable m_queue_cv;

        void WorkerThreadProc();
        void ProcessLoadRequest(LoadRequest& request);
        ExSharedPtr<void> LoadResource(const ResourceDesc& desc);
        void EvictLeastRecentlyUsed();
        uint64_t CalculateResourceSize(const ResourceItem& item) const;
    };

    /// 全局资源流式管理器实例
    extern UIResourceStreamingManager* g_resource_streaming_manager;

    /// 便捷宏定义
    #define RESOURCE_REGISTER(desc) if(g_resource_streaming_manager) g_resource_streaming_manager->RegisterResource(desc)
    #define RESOURCE_REQUEST(path, priority) if(g_resource_streaming_manager) g_resource_streaming_manager->RequestResource(path, priority)
    #define RESOURCE_PRELOAD(path) if(g_resource_streaming_manager) g_resource_streaming_manager->PreloadResource(path)
    #define RESOURCE_UPDATE_VISIBILITY(path, visible, distance) if(g_resource_streaming_manager) g_resource_streaming_manager->UpdateVisibility(path, visible, distance)
}
