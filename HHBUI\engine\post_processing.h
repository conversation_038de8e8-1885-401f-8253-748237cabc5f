/**
** =====================================================================================
**
**       文件名称: post_processing.h
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】后处理特效系统 - 高级着色器特效与后处理管线框架 （声明文件）
**
**       主要功能:
**       - 高级着色器特效（模糊、发光、阴影）
**       - 粒子系统支持与管理
**       - 后处理效果管线
**       - HDR渲染管线支持
**       - 色彩空间转换
**       - 自适应亮度调节
**       - 特效性能监控与优化
**
**       技术特性:
**       - 采用现代C++17标准与DirectX11 API
**       - 高性能GPU计算着色器
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 可配置的后处理管线
**       - 自适应质量控制系统
**       - 实时性能监控与统计分析
**
**       更新记录:
**       2025-07-31 v1.0.0.0 : 1. 创建后处理特效系统
**                             2. 实现高级着色器特效
**                             3. 添加粒子系统支持
**                             4. 完成HDR渲染管线
**                             5. 实现色彩空间转换
**                             6. 集成自适应亮度调节
**                             7. 确保性能监控优化
**
** =====================================================================================
**/

#pragma once
#include "render_api.h"
#include "common/smart_ptr.hpp"
#include <vector>
#include <memory>
#include <functional>
#include <DirectXMath.h>
#include <wrl.h>

namespace HHBUI
{
    /// 后处理效果类型
    enum class PostProcessEffect : uint32_t
    {
        NONE = 0,
        GAUSSIAN_BLUR,              // 高斯模糊
        BOX_BLUR,                   // 盒式模糊
        MOTION_BLUR,                // 运动模糊
        BLOOM,                      // 泛光效果
        GLOW,                       // 发光效果
        SHADOW_MAPPING,             // 阴影映射
        SCREEN_SPACE_SHADOW,        // 屏幕空间阴影
        DEPTH_OF_FIELD,             // 景深效果
        CHROMATIC_ABERRATION,       // 色差效果
        VIGNETTE,                   // 暗角效果
        COLOR_GRADING,              // 色彩分级
        TONE_MAPPING,               // 色调映射
        GAMMA_CORRECTION,           // 伽马校正
        EXPOSURE_ADJUSTMENT,        // 曝光调节
        CONTRAST_ENHANCEMENT,       // 对比度增强
        SATURATION_ADJUSTMENT,      // 饱和度调节
        CUSTOM                      // 自定义效果
    };

    /// 后处理质量级别
    enum class PostProcessQuality : uint32_t
    {
        LOW = 0,
        MEDIUM,
        HIGH,
        ULTRA
    };

    /// 模糊效果管理器
    class UIBlurEffectManager
    {
    public:
        UIBlurEffectManager();
        ~UIBlurEffectManager();

        /// 初始化模糊效果管理器
        HRESULT Initialize(ID3D11Device* device, uint32_t width, uint32_t height);

        /// 关闭模糊效果管理器
        void Shutdown();

        /// 应用高斯模糊
        HRESULT ApplyGaussianBlur(ID3D11DeviceContext* context,
                                 ID3D11ShaderResourceView* input_srv,
                                 ID3D11RenderTargetView* output_rtv,
                                 float blur_radius = 5.0f,
                                 PostProcessQuality quality = PostProcessQuality::MEDIUM);

        /// 应用盒式模糊
        HRESULT ApplyBoxBlur(ID3D11DeviceContext* context,
                            ID3D11ShaderResourceView* input_srv,
                            ID3D11RenderTargetView* output_rtv,
                            uint32_t blur_size = 5);

        /// 应用运动模糊
        HRESULT ApplyMotionBlur(ID3D11DeviceContext* context,
                               ID3D11ShaderResourceView* input_srv,
                               ID3D11ShaderResourceView* velocity_srv,
                               ID3D11RenderTargetView* output_rtv,
                               float blur_strength = 1.0f);

        /// 调整模糊设置
        HRESULT ResizeBlur(uint32_t width, uint32_t height);

    private:
        struct BlurConstants
        {
            DirectX::XMFLOAT2 inverse_screen_size;
            float blur_radius;
            uint32_t blur_size;
            DirectX::XMFLOAT4 blur_weights[16];
            DirectX::XMFLOAT2 blur_offsets[16];
        };

        Microsoft::WRL::ComPtr<ID3D11VertexShader> m_vertex_shader;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_gaussian_blur_ps;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_box_blur_ps;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_motion_blur_ps;
        Microsoft::WRL::ComPtr<ID3D11Buffer> m_constant_buffer;
        Microsoft::WRL::ComPtr<ID3D11SamplerState> m_sampler_state;
        
        // 中间渲染目标（用于双通道模糊）
        Microsoft::WRL::ComPtr<ID3D11Texture2D> m_intermediate_texture;
        Microsoft::WRL::ComPtr<ID3D11RenderTargetView> m_intermediate_rtv;
        Microsoft::WRL::ComPtr<ID3D11ShaderResourceView> m_intermediate_srv;
        
        uint32_t m_width;
        uint32_t m_height;
        BlurConstants m_constants;

        HRESULT CreateBlurShaders(ID3D11Device* device);
        HRESULT CreateIntermediateResources(ID3D11Device* device);
        void CalculateGaussianWeights(float sigma, uint32_t kernel_size);
    };

    /// 泛光效果管理器
    class UIBloomEffectManager
    {
    public:
        UIBloomEffectManager();
        ~UIBloomEffectManager();

        /// 初始化泛光效果管理器
        HRESULT Initialize(ID3D11Device* device, uint32_t width, uint32_t height);

        /// 关闭泛光效果管理器
        void Shutdown();

        /// 应用泛光效果
        HRESULT ApplyBloom(ID3D11DeviceContext* context,
                          ID3D11ShaderResourceView* input_srv,
                          ID3D11RenderTargetView* output_rtv,
                          float bloom_threshold = 1.0f,
                          float bloom_intensity = 1.0f,
                          float bloom_radius = 5.0f);

        /// 设置泛光参数
        void SetBloomParams(float threshold, float intensity, float radius, uint32_t mip_levels = 5);

        /// 调整泛光设置
        HRESULT ResizeBloom(uint32_t width, uint32_t height);

    private:
        struct BloomConstants
        {
            float bloom_threshold;
            float bloom_intensity;
            float bloom_radius;
            uint32_t mip_level;
            DirectX::XMFLOAT2 inverse_screen_size;
            DirectX::XMFLOAT2 padding;
        };

        Microsoft::WRL::ComPtr<ID3D11VertexShader> m_vertex_shader;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_threshold_ps;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_downsample_ps;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_upsample_ps;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_combine_ps;
        Microsoft::WRL::ComPtr<ID3D11Buffer> m_constant_buffer;
        Microsoft::WRL::ComPtr<ID3D11SamplerState> m_sampler_state;
        
        // Mipmap链
        std::vector<Microsoft::WRL::ComPtr<ID3D11Texture2D>> m_mip_textures;
        std::vector<Microsoft::WRL::ComPtr<ID3D11RenderTargetView>> m_mip_rtvs;
        std::vector<Microsoft::WRL::ComPtr<ID3D11ShaderResourceView>> m_mip_srvs;
        
        uint32_t m_width;
        uint32_t m_height;
        uint32_t m_mip_levels;
        BloomConstants m_constants;

        HRESULT CreateBloomShaders(ID3D11Device* device);
        HRESULT CreateMipChain(ID3D11Device* device);
    };

    /// 阴影效果管理器
    class UIShadowEffectManager
    {
    public:
        UIShadowEffectManager();
        ~UIShadowEffectManager();

        /// 初始化阴影效果管理器
        HRESULT Initialize(ID3D11Device* device, uint32_t shadow_map_size = 2048);

        /// 关闭阴影效果管理器
        void Shutdown();

        /// 创建阴影映射
        HRESULT CreateShadowMap(ID3D11DeviceContext* context,
                               const DirectX::XMFLOAT3& light_position,
                               const DirectX::XMFLOAT3& light_direction,
                               std::function<void()> render_scene_func);

        /// 应用阴影效果
        HRESULT ApplyShadows(ID3D11DeviceContext* context,
                            ID3D11ShaderResourceView* input_srv,
                            ID3D11RenderTargetView* output_rtv,
                            const DirectX::XMFLOAT4X4& light_view_proj_matrix);

        /// 应用屏幕空间阴影
        HRESULT ApplyScreenSpaceShadows(ID3D11DeviceContext* context,
                                       ID3D11ShaderResourceView* depth_srv,
                                       ID3D11RenderTargetView* output_rtv,
                                       const DirectX::XMFLOAT3& light_direction);

        /// 设置阴影参数
        void SetShadowParams(float shadow_bias = 0.001f, 
                            float shadow_intensity = 0.5f,
                            uint32_t pcf_samples = 4);

    private:
        struct ShadowConstants
        {
            DirectX::XMFLOAT4X4 light_view_proj_matrix;
            DirectX::XMFLOAT3 light_direction;
            float shadow_bias;
            float shadow_intensity;
            uint32_t pcf_samples;
            DirectX::XMFLOAT2 inverse_shadow_map_size;
        };

        Microsoft::WRL::ComPtr<ID3D11Texture2D> m_shadow_map_texture;
        Microsoft::WRL::ComPtr<ID3D11DepthStencilView> m_shadow_map_dsv;
        Microsoft::WRL::ComPtr<ID3D11ShaderResourceView> m_shadow_map_srv;
        
        Microsoft::WRL::ComPtr<ID3D11VertexShader> m_shadow_map_vs;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_shadow_map_ps;
        Microsoft::WRL::ComPtr<ID3D11VertexShader> m_shadow_apply_vs;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_shadow_apply_ps;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_screen_space_shadow_ps;
        
        Microsoft::WRL::ComPtr<ID3D11Buffer> m_constant_buffer;
        Microsoft::WRL::ComPtr<ID3D11SamplerState> m_shadow_sampler;
        Microsoft::WRL::ComPtr<ID3D11RasterizerState> m_shadow_rasterizer;
        Microsoft::WRL::ComPtr<ID3D11DepthStencilState> m_shadow_depth_state;
        
        uint32_t m_shadow_map_size;
        ShadowConstants m_constants;

        HRESULT CreateShadowResources(ID3D11Device* device);
        HRESULT CreateShadowShaders(ID3D11Device* device);
    };

    /// HDR渲染管理器
    class UIHDRManager
    {
    public:
        UIHDRManager();
        ~UIHDRManager();

        /// 初始化HDR管理器
        HRESULT Initialize(ID3D11Device* device, uint32_t width, uint32_t height);

        /// 关闭HDR管理器
        void Shutdown();

        /// 开始HDR渲染
        HRESULT BeginHDRRendering(ID3D11DeviceContext* context);

        /// 结束HDR渲染并应用色调映射
        HRESULT EndHDRRendering(ID3D11DeviceContext* context,
                               ID3D11RenderTargetView* ldr_output_rtv);

        /// 应用色调映射
        HRESULT ApplyToneMapping(ID3D11DeviceContext* context,
                                ID3D11ShaderResourceView* hdr_srv,
                                ID3D11RenderTargetView* ldr_rtv,
                                float exposure = 1.0f);

        /// 自适应亮度调节
        HRESULT UpdateAdaptiveLuminance(ID3D11DeviceContext* context,
                                       ID3D11ShaderResourceView* hdr_srv);

        /// 设置HDR参数
        void SetHDRParams(float exposure = 1.0f,
                         float white_point = 11.2f,
                         bool enable_adaptive = true);

        /// 获取HDR渲染目标
        ID3D11RenderTargetView* GetHDRRTV() const { return m_hdr_rtv.Get(); }

    private:
        struct HDRConstants
        {
            float exposure;
            float white_point;
            float average_luminance;
            float adaptation_rate;
            DirectX::XMFLOAT4 tone_mapping_params;
        };

        Microsoft::WRL::ComPtr<ID3D11Texture2D> m_hdr_texture;
        Microsoft::WRL::ComPtr<ID3D11RenderTargetView> m_hdr_rtv;
        Microsoft::WRL::ComPtr<ID3D11ShaderResourceView> m_hdr_srv;

        Microsoft::WRL::ComPtr<ID3D11Texture2D> m_luminance_texture;
        Microsoft::WRL::ComPtr<ID3D11ShaderResourceView> m_luminance_srv;
        Microsoft::WRL::ComPtr<ID3D11UnorderedAccessView> m_luminance_uav;

        Microsoft::WRL::ComPtr<ID3D11VertexShader> m_vertex_shader;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_tone_mapping_ps;
        Microsoft::WRL::ComPtr<ID3D11ComputeShader> m_luminance_cs;
        Microsoft::WRL::ComPtr<ID3D11Buffer> m_constant_buffer;
        Microsoft::WRL::ComPtr<ID3D11SamplerState> m_sampler_state;

        uint32_t m_width;
        uint32_t m_height;
        HDRConstants m_constants;
        float m_current_luminance;
        bool m_enable_adaptive;

        HRESULT CreateHDRResources(ID3D11Device* device);
        HRESULT CreateHDRShaders(ID3D11Device* device);
    };

    /// 色彩管理器
    class UIColorManager
    {
    public:
        UIColorManager();
        ~UIColorManager();

        /// 初始化色彩管理器
        HRESULT Initialize(ID3D11Device* device);

        /// 关闭色彩管理器
        void Shutdown();

        /// 色彩空间转换
        HRESULT ConvertColorSpace(ID3D11DeviceContext* context,
                                 ID3D11ShaderResourceView* input_srv,
                                 ID3D11RenderTargetView* output_rtv,
                                 uint32_t source_space,
                                 uint32_t target_space);

        /// 应用色彩分级
        HRESULT ApplyColorGrading(ID3D11DeviceContext* context,
                                 ID3D11ShaderResourceView* input_srv,
                                 ID3D11RenderTargetView* output_rtv,
                                 const DirectX::XMFLOAT3& shadows,
                                 const DirectX::XMFLOAT3& midtones,
                                 const DirectX::XMFLOAT3& highlights);

        /// 调整伽马值
        HRESULT ApplyGammaCorrection(ID3D11DeviceContext* context,
                                    ID3D11ShaderResourceView* input_srv,
                                    ID3D11RenderTargetView* output_rtv,
                                    float gamma = 2.2f);

        /// 调整对比度和饱和度
        HRESULT AdjustContrastSaturation(ID3D11DeviceContext* context,
                                        ID3D11ShaderResourceView* input_srv,
                                        ID3D11RenderTargetView* output_rtv,
                                        float contrast = 1.0f,
                                        float saturation = 1.0f);

    private:
        struct ColorConstants
        {
            DirectX::XMFLOAT4X4 color_matrix;
            DirectX::XMFLOAT3 shadows;
            float gamma;
            DirectX::XMFLOAT3 midtones;
            float contrast;
            DirectX::XMFLOAT3 highlights;
            float saturation;
        };

        Microsoft::WRL::ComPtr<ID3D11VertexShader> m_vertex_shader;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_color_space_ps;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_color_grading_ps;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_gamma_correction_ps;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_contrast_saturation_ps;
        Microsoft::WRL::ComPtr<ID3D11Buffer> m_constant_buffer;
        Microsoft::WRL::ComPtr<ID3D11SamplerState> m_sampler_state;

        ColorConstants m_constants;

        HRESULT CreateColorShaders(ID3D11Device* device);
        void SetupColorSpaceMatrix(uint32_t source_space, uint32_t target_space);
    };

    /// 后处理管线管理器
    class UIPostProcessPipeline
    {
    public:
        UIPostProcessPipeline();
        ~UIPostProcessPipeline();

        /// 初始化后处理管线
        HRESULT Initialize(ID3D11Device* device, ID3D11DeviceContext* context,
                          uint32_t width, uint32_t height);

        /// 关闭后处理管线
        void Shutdown();

        /// 添加后处理效果
        void AddEffect(PostProcessEffect effect, PostProcessQuality quality = PostProcessQuality::MEDIUM);

        /// 移除后处理效果
        void RemoveEffect(PostProcessEffect effect);

        /// 清空所有效果
        void ClearEffects();

        /// 执行后处理管线
        HRESULT ExecutePipeline(ID3D11ShaderResourceView* input_srv,
                               ID3D11RenderTargetView* output_rtv);

        /// 设置效果参数
        void SetEffectParams(PostProcessEffect effect, const void* params, size_t params_size);

        /// 获取效果管理器
        UIBlurEffectManager* GetBlurManager() { return m_blur_manager.get(); }
        UIBloomEffectManager* GetBloomManager() { return m_bloom_manager.get(); }
        UIShadowEffectManager* GetShadowManager() { return m_shadow_manager.get(); }
        UIHDRManager* GetHDRManager() { return m_hdr_manager.get(); }
        UIColorManager* GetColorManager() { return m_color_manager.get(); }

    private:
        struct EffectEntry
        {
            PostProcessEffect effect;
            PostProcessQuality quality;
            bool enabled;
        };

        ID3D11Device* m_device;
        ID3D11DeviceContext* m_context;

        // 效果管理器
        ExUniquePtr<UIBlurEffectManager> m_blur_manager;
        ExUniquePtr<UIBloomEffectManager> m_bloom_manager;
        ExUniquePtr<UIShadowEffectManager> m_shadow_manager;
        ExUniquePtr<UIHDRManager> m_hdr_manager;
        ExUniquePtr<UIColorManager> m_color_manager;

        // 效果列表
        std::vector<EffectEntry> m_effects;

        // 中间渲染目标
        std::vector<Microsoft::WRL::ComPtr<ID3D11Texture2D>> m_intermediate_textures;
        std::vector<Microsoft::WRL::ComPtr<ID3D11RenderTargetView>> m_intermediate_rtvs;
        std::vector<Microsoft::WRL::ComPtr<ID3D11ShaderResourceView>> m_intermediate_srvs;

        uint32_t m_width;
        uint32_t m_height;
        uint32_t m_current_target;

        HRESULT CreateIntermediateTargets();
        HRESULT ExecuteEffect(const EffectEntry& effect,
                             ID3D11ShaderResourceView* input_srv,
                             ID3D11RenderTargetView* output_rtv);
        void SwapTargets();
    };

    /// 全局后处理管理器实例
    extern UIPostProcessPipeline* g_post_process_pipeline;

    /// 便捷宏定义
    #define POST_PROCESS_ADD_EFFECT(effect, quality) if(g_post_process_pipeline) g_post_process_pipeline->AddEffect(effect, quality)
    #define POST_PROCESS_EXECUTE(input, output) if(g_post_process_pipeline) g_post_process_pipeline->ExecutePipeline(input, output)
}
