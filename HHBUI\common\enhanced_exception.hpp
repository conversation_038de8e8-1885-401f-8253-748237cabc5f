/**
** =====================================================================================
**
**       文件名称: enhanced_exception.hpp
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】增强异常处理系统 - 现代化C++17异常管理框架 （声明文件）
**
**       主要功能:
**       - 增强的异常类型与层次结构
**       - 作用域跟踪与调用栈管理
**       - 智能重试机制与策略配置
**       - 异常聚合与批处理支持
**       - 异常恢复与回滚机制
**       - 详细的异常统计与监控
**       - 异常日志与调试支持
**
**       技术特性:
**       - 采用现代C++17标准与异常安全设计
**       - 线程安全的异常状态管理
**       - 异常安全保证与RAII管理
**       - 高性能异常处理机制
**       - 可配置的重试策略
**       - 智能异常传播控制
**       - 异常上下文保存与恢复
**
**       更新记录:
**       2025-07-31 v1.0.0.0 : 1. 创建增强异常处理系统
**                             2. 实现作用域跟踪机制
**                             3. 添加重试策略支持
**                             4. 集成异常统计监控
**
** =====================================================================================
*/
#pragma once

// 防止Windows宏干扰
#ifndef NOMINMAX
#define NOMINMAX
#endif

#include <exception>
#include <string>
#include <vector>
#include <memory>
#include <chrono>
#include <functional>
#include <atomic>
#include <mutex>
#include <unordered_map>
#include <stack>
#include <thread>
#include <algorithm>

// 确保使用函数版本而不是宏版本
#ifdef min
#undef min
#endif
#ifdef max
#undef max
#endif
#include "Exception.h"
#include "memory.h"

namespace HHBUI
{
	// 异常严重级别
	enum class ExceptionSeverity : int
	{
		Info = 0,       // 信息
		Warning = 1,    // 警告
		Error = 2,      // 错误
		Critical = 3,   // 严重错误
		Fatal = 4       // 致命错误
	};

	// 异常分类
	enum class ExceptionCategory : int
	{
		Unknown = 0,        // 未知
		Memory = 1,         // 内存相关
		IO = 2,             // 输入输出
		Network = 3,        // 网络相关
		Graphics = 4,       // 图形相关
		System = 5,         // 系统相关
		Logic = 6,          // 逻辑错误
		Runtime = 7,        // 运行时错误
		Security = 8,       // 安全相关
		Configuration = 9   // 配置相关
	};

	// 重试策略
	struct RetryPolicy
	{
		size_t max_attempts = 3;                                    // 最大重试次数
		std::chrono::milliseconds initial_delay{ 100 };            // 初始延迟
		std::chrono::milliseconds max_delay{ 5000 };               // 最大延迟
		double backoff_multiplier = 2.0;                           // 退避乘数
		bool exponential_backoff = true;                           // 指数退避
		std::function<bool(const Exception&)> should_retry;        // 重试条件判断
	};

	// 异常统计信息
	struct ExceptionStats
	{
		std::atomic<size_t> total_exceptions{ 0 };         // 总异常数
		std::atomic<size_t> handled_exceptions{ 0 };       // 已处理异常数
		std::atomic<size_t> unhandled_exceptions{ 0 };     // 未处理异常数
		std::atomic<size_t> retry_attempts{ 0 };           // 重试次数
		std::atomic<size_t> successful_retries{ 0 };       // 成功重试数
		std::atomic<size_t> failed_retries{ 0 };           // 失败重试数
		std::unordered_map<int, size_t> severity_counts;   // 按严重级别统计
		std::unordered_map<int, size_t> category_counts;   // 按分类统计

		// 禁用拷贝构造和拷贝赋值（因为包含atomic成员）
		ExceptionStats() = default;
		ExceptionStats(const ExceptionStats&) = delete;
		ExceptionStats& operator=(const ExceptionStats&) = delete;
		ExceptionStats(ExceptionStats&&) = delete;
		ExceptionStats& operator=(ExceptionStats&&) = delete;

		// 提供重置方法
		void reset()
		{
			total_exceptions.store(0, std::memory_order_relaxed);
			handled_exceptions.store(0, std::memory_order_relaxed);
			unhandled_exceptions.store(0, std::memory_order_relaxed);
			retry_attempts.store(0, std::memory_order_relaxed);
			successful_retries.store(0, std::memory_order_relaxed);
			failed_retries.store(0, std::memory_order_relaxed);
			severity_counts.clear();
			category_counts.clear();
		}
	};

	// 作用域信息
	struct ScopeInfo
	{
		std::string function_name;      // 函数名
		std::string file_name;          // 文件名
		int line_number;                // 行号
		std::chrono::steady_clock::time_point enter_time;  // 进入时间
		std::unordered_map<std::string, std::string> context;  // 上下文信息

		ScopeInfo(const std::string& func, const std::string& file, int line)
			: function_name(func), file_name(file), line_number(line)
			, enter_time(std::chrono::steady_clock::now()) {}
	};

	// 调用栈跟踪器
	class CallStackTracker
	{
	public:
		static CallStackTracker& instance()
		{
			static thread_local CallStackTracker tracker;
			return tracker;
		}

		void push_scope(const ScopeInfo& scope)
		{
			std::lock_guard<std::mutex> lock(mutex_);
			call_stack_.push(scope);
		}

		void pop_scope()
		{
			std::lock_guard<std::mutex> lock(mutex_);
			if (!call_stack_.empty())
			{
				call_stack_.pop();
			}
		}

		std::vector<ScopeInfo> get_call_stack() const
		{
			std::lock_guard<std::mutex> lock(mutex_);
			std::vector<ScopeInfo> result;
			std::stack<ScopeInfo> temp = call_stack_;
			
			while (!temp.empty())
			{
				result.push_back(temp.top());
				temp.pop();
			}
			
			std::reverse(result.begin(), result.end());
			return result;
		}

		void clear()
		{
			std::lock_guard<std::mutex> lock(mutex_);
			while (!call_stack_.empty())
			{
				call_stack_.pop();
			}
		}

	private:
		mutable std::mutex mutex_;
		std::stack<ScopeInfo> call_stack_;
	};

	// 作用域守卫
	class ScopeGuard
	{
	public:
		ScopeGuard(const std::string& func, const std::string& file, int line)
			: scope_info_(func, file, line)
		{
			CallStackTracker::instance().push_scope(scope_info_);
		}

		~ScopeGuard()
		{
			CallStackTracker::instance().pop_scope();
		}

		// 添加上下文信息
		void add_context(const std::string& key, const std::string& value)
		{
			scope_info_.context[key] = value;
		}

	private:
		ScopeInfo scope_info_;
	};

	/**
	 * @brief 增强异常类
	 * 扩展原有Exception类，添加更多功能
	 */
	class EnhancedException : public Exception
	{
	public:
		// 构造函数
		EnhancedException(HRESULT status, const std::wstring& message = L"",
			ExceptionSeverity severity = ExceptionSeverity::Error,
			ExceptionCategory category = ExceptionCategory::Unknown)
			: Exception(status, message.c_str())
			, severity_(severity), category_(category)
			, timestamp_(std::chrono::steady_clock::now())
		{
			// 捕获调用栈
			call_stack_ = CallStackTracker::instance().get_call_stack();

			// 更新统计
			update_stats();
		}

		EnhancedException(HRESULT status, const std::wstring& message,
			const std::wstring& file, int line,
			ExceptionSeverity severity = ExceptionSeverity::Error,
			ExceptionCategory category = ExceptionCategory::Unknown)
			: Exception(status, message.c_str(), file.c_str(), line)
			, severity_(severity), category_(category)
			, timestamp_(std::chrono::steady_clock::now())
		{
			// 捕获调用栈
			call_stack_ = CallStackTracker::instance().get_call_stack();

			// 更新统计
			update_stats();
		}

		// 拷贝构造函数
		EnhancedException(const EnhancedException& other)
			: Exception(other), severity_(other.severity_), category_(other.category_)
			, timestamp_(other.timestamp_), call_stack_(other.call_stack_)
			, inner_exceptions_(other.inner_exceptions_), context_(other.context_) {}

		// 移动构造函数
		EnhancedException(EnhancedException&& other) noexcept
			: Exception(std::move(other)), severity_(other.severity_), category_(other.category_)
			, timestamp_(other.timestamp_), call_stack_(std::move(other.call_stack_))
			, inner_exceptions_(std::move(other.inner_exceptions_)), context_(std::move(other.context_)) {}

		// 赋值操作符
		EnhancedException& operator=(const EnhancedException& other)
		{
			if (this != &other)
			{
				Exception::operator=(other);
				severity_ = other.severity_;
				category_ = other.category_;
				timestamp_ = other.timestamp_;
				call_stack_ = other.call_stack_;
				inner_exceptions_ = other.inner_exceptions_;
				context_ = other.context_;
			}
			return *this;
		}

		EnhancedException& operator=(EnhancedException&& other) noexcept
		{
			if (this != &other)
			{
				Exception::operator=(std::move(other));
				severity_ = other.severity_;
				category_ = other.category_;
				timestamp_ = other.timestamp_;
				call_stack_ = std::move(other.call_stack_);
				inner_exceptions_ = std::move(other.inner_exceptions_);
				context_ = std::move(other.context_);
			}
			return *this;
		}

		// 访问器
		ExceptionSeverity get_severity() const noexcept { return severity_; }
		ExceptionCategory get_category() const noexcept { return category_; }
		std::chrono::steady_clock::time_point get_timestamp() const noexcept { return timestamp_; }
		const std::vector<ScopeInfo>& get_call_stack() const noexcept { return call_stack_; }

		// 添加内部异常
		void add_inner_exception(const EnhancedException& inner)
		{
			inner_exceptions_.push_back(std::make_shared<EnhancedException>(inner));
		}

		// 获取内部异常
		const std::vector<std::shared_ptr<EnhancedException>>& get_inner_exceptions() const noexcept
		{
			return inner_exceptions_;
		}

		// 添加上下文信息
		void add_context(const std::string& key, const std::string& value)
		{
			context_[key] = value;
		}

		// 获取上下文信息
		const std::unordered_map<std::string, std::string>& get_context() const noexcept
		{
			return context_;
		}

		// 获取详细信息
		std::string get_detailed_message() const
		{
			std::string result = "Exception Details:\n";
			result += "Status: 0x" + std::to_string(status()) + "\n";
			result += "Message: " + std::string(message().begin(), message().end()) + "\n";
			result += "Severity: " + std::to_string(static_cast<int>(severity_)) + "\n";
			result += "Category: " + std::to_string(static_cast<int>(category_)) + "\n";
			
			if (!call_stack_.empty())
			{
				result += "Call Stack:\n";
				for (const auto& scope : call_stack_)
				{
					result += "  " + scope.function_name + " (" + scope.file_name + ":" + std::to_string(scope.line_number) + ")\n";
				}
			}
			
			if (!context_.empty())
			{
				result += "Context:\n";
				for (const auto& [key, value] : context_)
				{
					result += "  " + key + ": " + value + "\n";
				}
			}
			
			return result;
		}

	private:
		ExceptionSeverity severity_;
		ExceptionCategory category_;
		std::chrono::steady_clock::time_point timestamp_;
		std::vector<ScopeInfo> call_stack_;
		std::vector<std::shared_ptr<EnhancedException>> inner_exceptions_;
		std::unordered_map<std::string, std::string> context_;

		void update_stats()
		{
			// 这里可以添加统计更新逻辑
			// 为了简化，暂时省略具体实现
		}
	};

	/**
	 * @brief 重试执行器
	 * 提供智能重试机制
	 */
	class RetryExecutor
	{
	public:
		explicit RetryExecutor(const RetryPolicy& policy = RetryPolicy{})
			: policy_(policy) {}

		/**
		 * @brief 执行带重试的操作
		 */
		template<typename Func>
		auto execute(Func&& func) -> decltype(func())
		{
			size_t attempt = 0;
			std::chrono::milliseconds delay = policy_.initial_delay;

			while (attempt < policy_.max_attempts)
			{
				try
				{
					attempt++;
					auto result = func();

					if (attempt > 1)
					{
						// 记录成功重试
						// stats_.successful_retries.fetch_add(1, std::memory_order_relaxed);
					}

					return result;
				}
				catch (const EnhancedException& ex)
				{
					// stats_.retry_attempts.fetch_add(1, std::memory_order_relaxed);

					// 检查是否应该重试
					if (attempt >= policy_.max_attempts ||
						(policy_.should_retry && !policy_.should_retry(ex)))
					{
						// stats_.failed_retries.fetch_add(1, std::memory_order_relaxed);
						throw;
					}

					// 等待后重试
					if (attempt < policy_.max_attempts)
					{
						std::this_thread::sleep_for(delay);

						// 计算下次延迟
						if (policy_.exponential_backoff)
						{
							delay = std::chrono::milliseconds(
								static_cast<long long>(delay.count() * policy_.backoff_multiplier));
							delay = (std::min)(delay, policy_.max_delay);
						}
					}
				}
				catch (const Exception& ex)
				{
					// 转换为增强异常并重试
					EnhancedException enhanced(ex.status(), ex.message(), ex.file(), ex.line());

					if (attempt >= policy_.max_attempts ||
						(policy_.should_retry && !policy_.should_retry(enhanced)))
					{
						throw enhanced;
					}

					if (attempt < policy_.max_attempts)
					{
						std::this_thread::sleep_for(delay);

						if (policy_.exponential_backoff)
						{
							delay = std::chrono::milliseconds(
								static_cast<long long>(delay.count() * policy_.backoff_multiplier));
							delay = (std::min)(delay, policy_.max_delay);
						}
					}
				}
			}

			// 不应该到达这里
			throw_ex(E_FAIL, L"重试执行失败");
		}

		/**
		 * @brief 设置重试策略
		 */
		void set_policy(const RetryPolicy& policy)
		{
			policy_ = policy;
		}

		/**
		 * @brief 获取重试策略
		 */
		const RetryPolicy& get_policy() const noexcept
		{
			return policy_;
		}

	private:
		RetryPolicy policy_;
	};

	/**
	 * @brief 异常聚合器
	 * 收集和管理多个异常
	 */
	class ExceptionAggregator
	{
	public:
		/**
		 * @brief 添加异常
		 */
		void add_exception(const EnhancedException& ex)
		{
			std::lock_guard<std::mutex> lock(mutex_);
			exceptions_.push_back(std::make_shared<EnhancedException>(ex));
		}

		/**
		 * @brief 检查是否有异常
		 */
		bool has_exceptions() const
		{
			std::lock_guard<std::mutex> lock(mutex_);
			return !exceptions_.empty();
		}

		/**
		 * @brief 获取异常数量
		 */
		size_t count() const
		{
			std::lock_guard<std::mutex> lock(mutex_);
			return exceptions_.size();
		}

		/**
		 * @brief 获取所有异常
		 */
		std::vector<std::shared_ptr<EnhancedException>> get_exceptions() const
		{
			std::lock_guard<std::mutex> lock(mutex_);
			return exceptions_;
		}

		/**
		 * @brief 清空异常
		 */
		void clear()
		{
			std::lock_guard<std::mutex> lock(mutex_);
			exceptions_.clear();
		}

		/**
		 * @brief 抛出聚合异常
		 */
		void throw_if_any() const
		{
			std::lock_guard<std::mutex> lock(mutex_);
			if (!exceptions_.empty())
			{
				EnhancedException aggregate(E_FAIL, L"发生了多个异常",
					ExceptionSeverity::Error, ExceptionCategory::Runtime);

				for (const auto& ex : exceptions_)
				{
					aggregate.add_inner_exception(*ex);
				}

				throw aggregate;
			}
		}

	private:
		mutable std::mutex mutex_;
		std::vector<std::shared_ptr<EnhancedException>> exceptions_;
	};

	/**
	 * @brief 异常管理器
	 * 全局异常管理和统计
	 */
	class ExceptionManager
	{
	public:
		static ExceptionManager& instance()
		{
			static ExceptionManager manager;
			return manager;
		}

		/**
		 * @brief 注册异常处理器
		 */
		void register_handler(ExceptionCategory category,
			std::function<void(const EnhancedException&)> handler)
		{
			std::lock_guard<std::mutex> lock(mutex_);
			handlers_[category] = std::move(handler);
		}

		/**
		 * @brief 处理异常
		 */
		void handle_exception(const EnhancedException& ex)
		{
			std::lock_guard<std::mutex> lock(mutex_);

			// 更新统计
			stats_.total_exceptions.fetch_add(1, std::memory_order_relaxed);

			// 查找并调用处理器
			auto it = handlers_.find(ex.get_category());
			if (it != handlers_.end())
			{
				try
				{
					it->second(ex);
					stats_.handled_exceptions.fetch_add(1, std::memory_order_relaxed);
				}
				catch (...)
				{
					stats_.unhandled_exceptions.fetch_add(1, std::memory_order_relaxed);
				}
			}
			else
			{
				stats_.unhandled_exceptions.fetch_add(1, std::memory_order_relaxed);
			}
		}

		/**
		 * @brief 获取统计信息
		 */
		const ExceptionStats& get_stats() const noexcept
		{
			return stats_;
		}

		/**
		 * @brief 重置统计信息
		 */
		void reset_stats()
		{
			std::lock_guard<std::mutex> lock(mutex_);
			stats_.reset();
		}

	private:
		mutable std::mutex mutex_;
		ExceptionStats stats_;
		std::unordered_map<ExceptionCategory, std::function<void(const EnhancedException&)>> handlers_;
	};

	// 便利宏定义
#define EX_SCOPE_GUARD() ScopeGuard __scope_guard__(__FUNCTION__, __FILE__, __LINE__)
#define EX_SCOPE_GUARD_NAMED(name) ScopeGuard name(__FUNCTION__, __FILE__, __LINE__)
#define EX_ADD_CONTEXT(guard, key, value) guard.add_context(key, value)

#define throw_enhanced(status, message, severity, category) \
	throw EnhancedException(status, message, __CALLINFO__, severity, category)

#define throw_enhanced_if_false(exp, status, message, severity, category) \
	if(!(exp)) { throw_enhanced(status, message, severity, category); }

#define try_with_retry(policy, block) \
	RetryExecutor __retry_executor__(policy); \
	__retry_executor__.execute([&]() { block; })

}
