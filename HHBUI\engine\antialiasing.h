/**
** =====================================================================================
**
**       文件名称: antialiasing.h
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】抗锯齿技术系统 - 高质量抗锯齿与亚像素渲染框架 （声明文件）
**
**       主要功能:
**       - MSAA多重采样抗锯齿技术
**       - FXAA快速近似抗锯齿算法
**       - TAA时间抗锯齿技术
**       - 亚像素渲染支持
**       - 文本渲染清晰度优化
**       - 自适应抗锯齿质量调节
**       - 抗锯齿性能监控与统计
**
**       技术特性:
**       - 采用现代C++17标准与DirectX11 API
**       - 多种抗锯齿算法集成
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能抗锯齿算法实现
**       - 自适应质量控制系统
**       - 实时性能监控与统计分析
**
**       更新记录:
**       2025-07-31 v1.0.0.0 : 1. 创建抗锯齿技术系统
**                             2. 实现MSAA抗锯齿支持
**                             3. 添加FXAA算法实现
**                             4. 完成TAA技术集成
**                             5. 实现亚像素渲染
**                             6. 优化文本渲染清晰度
**                             7. 集成自适应质量控制
**
** =====================================================================================
**/

#pragma once
#include "render_api.h"
#include "common/smart_ptr.hpp"
#include <vector>
#include <memory>
#include <DirectXMath.h>
#include <wrl.h>

namespace HHBUI
{
    /// 抗锯齿类型
    enum class AntiAliasingType : uint32_t
    {
        NONE = 0,                   // 无抗锯齿
        MSAA_2X,                    // 2x多重采样
        MSAA_4X,                    // 4x多重采样
        MSAA_8X,                    // 8x多重采样
        FXAA,                       // 快速近似抗锯齿
        TAA,                        // 时间抗锯齿
        SMAA,                       // 子像素形态抗锯齿
        HYBRID                      // 混合抗锯齿
    };

    /// 抗锯齿质量级别
    enum class AntiAliasingQuality : uint32_t
    {
        LOW = 0,
        MEDIUM,
        HIGH,
        ULTRA
    };

    /// MSAA抗锯齿管理器
    class UIMSAAManager
    {
    public:
        UIMSAAManager();
        ~UIMSAAManager();

        /// 初始化MSAA管理器
        HRESULT Initialize(ID3D11Device* device, uint32_t width, uint32_t height, 
                          uint32_t sample_count = 4);

        /// 关闭MSAA管理器
        void Shutdown();

        /// 调整MSAA设置
        HRESULT ResizeMSAA(uint32_t width, uint32_t height, uint32_t sample_count);

        /// 开始MSAA渲染
        HRESULT BeginMSAA(ID3D11DeviceContext* context);

        /// 结束MSAA渲染
        HRESULT EndMSAA(ID3D11DeviceContext* context);

        /// 解析MSAA到后备缓冲区
        HRESULT ResolveMSAA(ID3D11DeviceContext* context, ID3D11Texture2D* back_buffer);

        /// 获取MSAA渲染目标视图
        ID3D11RenderTargetView* GetMSAARTV() const { return m_msaa_rtv.Get(); }

        /// 获取MSAA深度模板视图
        ID3D11DepthStencilView* GetMSAADSV() const { return m_msaa_dsv.Get(); }

        /// 获取采样数量
        uint32_t GetSampleCount() const { return m_sample_count; }

        /// 检查MSAA支持
        static bool CheckMSAASupport(ID3D11Device* device, DXGI_FORMAT format, uint32_t sample_count);

    private:
        Microsoft::WRL::ComPtr<ID3D11Texture2D> m_msaa_texture;
        Microsoft::WRL::ComPtr<ID3D11Texture2D> m_msaa_depth_texture;
        Microsoft::WRL::ComPtr<ID3D11RenderTargetView> m_msaa_rtv;
        Microsoft::WRL::ComPtr<ID3D11DepthStencilView> m_msaa_dsv;
        Microsoft::WRL::ComPtr<ID3D11ShaderResourceView> m_msaa_srv;
        
        uint32_t m_width;
        uint32_t m_height;
        uint32_t m_sample_count;
        DXGI_FORMAT m_format;

        HRESULT CreateMSAAResources(ID3D11Device* device);
    };

    /// FXAA抗锯齿管理器
    class UIFXAAManager
    {
    public:
        UIFXAAManager();
        ~UIFXAAManager();

        /// 初始化FXAA管理器
        HRESULT Initialize(ID3D11Device* device, uint32_t width, uint32_t height);

        /// 关闭FXAA管理器
        void Shutdown();

        /// 调整FXAA设置
        HRESULT ResizeFXAA(uint32_t width, uint32_t height);

        /// 应用FXAA处理
        HRESULT ApplyFXAA(ID3D11DeviceContext* context, 
                          ID3D11ShaderResourceView* input_srv,
                          ID3D11RenderTargetView* output_rtv,
                          AntiAliasingQuality quality = AntiAliasingQuality::MEDIUM);

        /// 设置FXAA参数
        void SetFXAAParams(float edge_threshold = 0.166f, 
                          float edge_threshold_min = 0.0833f,
                          float subpixel_quality = 0.75f);

    private:
        struct FXAAConstants
        {
            DirectX::XMFLOAT2 inverse_screen_size;
            float edge_threshold;
            float edge_threshold_min;
            float subpixel_quality;
            DirectX::XMFLOAT3 padding;
        };

        Microsoft::WRL::ComPtr<ID3D11VertexShader> m_vertex_shader;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_pixel_shader;
        Microsoft::WRL::ComPtr<ID3D11Buffer> m_constant_buffer;
        Microsoft::WRL::ComPtr<ID3D11SamplerState> m_sampler_state;
        Microsoft::WRL::ComPtr<ID3D11BlendState> m_blend_state;
        Microsoft::WRL::ComPtr<ID3D11RasterizerState> m_rasterizer_state;
        
        uint32_t m_width;
        uint32_t m_height;
        FXAAConstants m_constants;

        HRESULT CreateFXAAShaders(ID3D11Device* device);
        HRESULT CreateFXAAStates(ID3D11Device* device);
    };

    /// TAA抗锯齿管理器
    class UITAAManager
    {
    public:
        UITAAManager();
        ~UITAAManager();

        /// 初始化TAA管理器
        HRESULT Initialize(ID3D11Device* device, uint32_t width, uint32_t height);

        /// 关闭TAA管理器
        void Shutdown();

        /// 调整TAA设置
        HRESULT ResizeTAA(uint32_t width, uint32_t height);

        /// 应用TAA处理
        HRESULT ApplyTAA(ID3D11DeviceContext* context,
                        ID3D11ShaderResourceView* current_frame_srv,
                        ID3D11ShaderResourceView* velocity_srv,
                        ID3D11RenderTargetView* output_rtv);

        /// 设置TAA参数
        void SetTAAParams(float feedback_factor = 0.9f, 
                         float velocity_scale = 1.0f,
                         bool enable_variance_clipping = true);

        /// 获取历史缓冲区
        ID3D11ShaderResourceView* GetHistoryBuffer() const { return m_history_srv.Get(); }

    private:
        struct TAAConstants
        {
            DirectX::XMFLOAT2 inverse_screen_size;
            float feedback_factor;
            float velocity_scale;
            DirectX::XMFLOAT4 jitter_offset;
            uint32_t frame_index;
            uint32_t enable_variance_clipping;
            DirectX::XMFLOAT2 padding;
        };

        Microsoft::WRL::ComPtr<ID3D11Texture2D> m_history_texture;
        Microsoft::WRL::ComPtr<ID3D11ShaderResourceView> m_history_srv;
        Microsoft::WRL::ComPtr<ID3D11RenderTargetView> m_history_rtv;
        
        Microsoft::WRL::ComPtr<ID3D11VertexShader> m_vertex_shader;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_pixel_shader;
        Microsoft::WRL::ComPtr<ID3D11Buffer> m_constant_buffer;
        Microsoft::WRL::ComPtr<ID3D11SamplerState> m_sampler_state;
        
        uint32_t m_width;
        uint32_t m_height;
        uint32_t m_frame_index;
        TAAConstants m_constants;
        
        std::vector<DirectX::XMFLOAT2> m_jitter_sequence;
        uint32_t m_jitter_index;

        HRESULT CreateTAAResources(ID3D11Device* device);
        HRESULT CreateTAAShaders(ID3D11Device* device);
        void GenerateJitterSequence();
        DirectX::XMFLOAT2 GetCurrentJitter();
    };

    /// 亚像素渲染管理器
    class UISubpixelRenderer
    {
    public:
        UISubpixelRenderer();
        ~UISubpixelRenderer();

        /// 初始化亚像素渲染器
        HRESULT Initialize(ID3D11Device* device);

        /// 关闭亚像素渲染器
        void Shutdown();

        /// 渲染亚像素文本
        HRESULT RenderSubpixelText(ID3D11DeviceContext* context,
                                  LPCWSTR text, 
                                  const DirectX::XMFLOAT2& position,
                                  ID3D11ShaderResourceView* font_atlas,
                                  const DirectX::XMFLOAT4& color);

        /// 设置亚像素参数
        void SetSubpixelParams(float gamma = 2.2f, 
                              float contrast = 1.0f,
                              bool enable_bgr_order = false);

    private:
        struct SubpixelConstants
        {
            DirectX::XMFLOAT4 text_color;
            DirectX::XMFLOAT2 position;
            float gamma;
            float contrast;
            uint32_t enable_bgr_order;
            DirectX::XMFLOAT3 padding;
        };

        Microsoft::WRL::ComPtr<ID3D11VertexShader> m_vertex_shader;
        Microsoft::WRL::ComPtr<ID3D11PixelShader> m_pixel_shader;
        Microsoft::WRL::ComPtr<ID3D11Buffer> m_constant_buffer;
        Microsoft::WRL::ComPtr<ID3D11SamplerState> m_sampler_state;
        
        SubpixelConstants m_constants;

        HRESULT CreateSubpixelShaders(ID3D11Device* device);
    };

    /// 抗锯齿管理器
    class UIAntiAliasingManager
    {
    public:
        UIAntiAliasingManager();
        ~UIAntiAliasingManager();

        /// 初始化抗锯齿管理器
        HRESULT Initialize(ID3D11Device* device, ID3D11DeviceContext* context,
                          uint32_t width, uint32_t height);

        /// 关闭抗锯齿管理器
        void Shutdown();

        /// 设置抗锯齿类型
        HRESULT SetAntiAliasingType(AntiAliasingType type, AntiAliasingQuality quality = AntiAliasingQuality::MEDIUM);

        /// 调整抗锯齿设置
        HRESULT ResizeAntiAliasing(uint32_t width, uint32_t height);

        /// 开始抗锯齿渲染
        HRESULT BeginAntiAliasing();

        /// 结束抗锯齿渲染
        HRESULT EndAntiAliasing(ID3D11RenderTargetView* final_rtv);

        /// 应用抗锯齿处理
        HRESULT ApplyAntiAliasing(ID3D11ShaderResourceView* input_srv,
                                 ID3D11RenderTargetView* output_rtv);

        /// 获取当前抗锯齿类型
        AntiAliasingType GetCurrentType() const { return m_current_type; }

        /// 获取当前质量级别
        AntiAliasingQuality GetCurrentQuality() const { return m_current_quality; }

        /// 自适应质量调节
        void EnableAdaptiveQuality(bool enable, float target_frametime_ms = 16.67f);

        /// 获取统计信息
        struct AAStats
        {
            AntiAliasingType current_type;
            AntiAliasingQuality current_quality;
            float processing_time_ms;
            uint32_t quality_adjustments;
            bool adaptive_enabled;
        };
        const AAStats& GetStats() const { return m_stats; }

    private:
        ID3D11Device* m_device;
        ID3D11DeviceContext* m_context;

        // 抗锯齿管理器实例
        ExUniquePtr<UIMSAAManager> m_msaa_manager;
        ExUniquePtr<UIFXAAManager> m_fxaa_manager;
        ExUniquePtr<UITAAManager> m_taa_manager;
        ExUniquePtr<UISubpixelRenderer> m_subpixel_renderer;

        // 中间渲染目标
        Microsoft::WRL::ComPtr<ID3D11Texture2D> m_intermediate_texture;
        Microsoft::WRL::ComPtr<ID3D11RenderTargetView> m_intermediate_rtv;
        Microsoft::WRL::ComPtr<ID3D11ShaderResourceView> m_intermediate_srv;

        // 当前设置
        AntiAliasingType m_current_type;
        AntiAliasingQuality m_current_quality;
        uint32_t m_width;
        uint32_t m_height;

        // 自适应质量控制
        bool m_adaptive_enabled;
        float m_target_frametime_ms;
        std::vector<float> m_frametime_history;
        uint32_t m_frametime_index;

        // 统计信息
        AAStats m_stats;
        std::chrono::high_resolution_clock::time_point m_start_time;

        HRESULT CreateIntermediateResources();
        void UpdateAdaptiveQuality(float current_frametime_ms);
        AntiAliasingQuality GetNextLowerQuality(AntiAliasingQuality current);
        AntiAliasingQuality GetNextHigherQuality(AntiAliasingQuality current);
    };

    /// 全局抗锯齿管理器实例
    extern UIAntiAliasingManager* g_antialiasing_manager;

    /// 便捷宏定义
    #define AA_SET_TYPE(type, quality) if(g_antialiasing_manager) g_antialiasing_manager->SetAntiAliasingType(type, quality)
    #define AA_BEGIN() if(g_antialiasing_manager) g_antialiasing_manager->BeginAntiAliasing()
    #define AA_END(rtv) if(g_antialiasing_manager) g_antialiasing_manager->EndAntiAliasing(rtv)
    #define AA_APPLY(input, output) if(g_antialiasing_manager) g_antialiasing_manager->ApplyAntiAliasing(input, output)

    /// RAII抗锯齿渲染管理类
    class ScopedAntiAliasing
    {
    public:
        ScopedAntiAliasing()
        {
            AA_BEGIN();
        }

        ~ScopedAntiAliasing()
        {
            // 注意：需要在外部调用AA_END，因为需要传递RTV参数
        }
    };

    #define AA_SCOPED() ScopedAntiAliasing _scoped_aa
}
