﻿#pragma once
namespace HHBUI
{
	// 前向声明
	class UIarray;
	enum LayoutFlags
	{
		elt_null = 0,                    //无
		elt_linear = 1,                    //线性
		elt_flow = 2,                    //流式
		elt_page = 3,                    //页面
		elt_table = 4,                    //表格
		elt_relative = 5,                    //相对
		elt_absolute = 6,                    //绝对

		//通用布局属性
		elp_padding_left = -1,                   //通用_内间距_左
		elp_padding_top = -2,                   //通用_内间距_顶
		elp_padding_right = -3,                   //通用_内间距_右
		elp_padding_bottom = -4,                   //通用_内间距_底
		elcp_margin_left = -1,                   //通用_外间距_左
		elcp_margin_top = -2,                   //通用_外间距_顶
		elcp_margin_right = -3,                   //通用_外间距_右
		elcp_margin_bottom = -4,                   //通用_外间距_底

		//线性布局属性
		elp_linear_direction = 1,                    //排布方向
		elcp_linear_size = 1,                    //尺寸 [-1或未填写为组件当前尺寸]
		elcp_linear_align = 2,                    //另外一个方向对齐方式
		elp_linear_dalign = 2,                    //布局方向对齐方式

		//线性布局向对齐方式
		elp_linear_dalign_left_top = 0,                    //左上
		elp_linear_dalign_center = 1,                    //居中
		elp_linear_dalign_right_bottom = 2,                    //右下

		//线性布局另一个方向对齐方式
		elcp_linear_algin_fill = 0,                    //填满
		elcp_linear_align_left_top = 1,                    //左上
		elcp_linear_align_center = 2,                    //居中
		elcp_linear_align_right_top = 3,                    //右上
		elcp_linear_align_right_bottom = 4,                    //右下

		//布局排布方向
		elp_direction_h = 0,                    //水平
		elp_direction_v = 1,                    //垂直

		//流式布局属性
		elp_flow_direction = 1,                    //排布方向
		elcp_flow_size = 1,                    //尺寸 [-1或未填写为组件当前尺寸]
		elcp_flow_new_line = 2,                    //组件强制换行

		//页面布局属性
		elp_page_current = 1,                    //当前显示页面索引
		elcp_page_fill = 1,                    //是否填充整个布局

		//表格布局属性
		elp_table_array_row = 1,                    //行高数组
		elp_table_array_cell = 2,                    //列宽数组
		elcp_table_row = 1,                    //所在行
		elcp_table_cell = 2,                    //所在列
		elcp_table_row_span = 3,                    //跨行数
		elcp_table_cell_span = 4,                    //跨列数
		elcp_table_fill = 5,                    //是否填满

		//相对布局属性件
		elcp_relative_left_of = 1,                    //左侧于组件
		elcp_relative_top_of = 2,                    //之上于组件
		elcp_relative_right_of = 3,                    //右侧于组件
		elcp_relative_bottom_of = 4,                    //之下于组件
		elcp_relative_left_align_of = 5,                    //左对齐于组件
		elcp_relative_top_align_of = 6,                    //顶对齐于组件
		elcp_relative_right_align_of = 7,                    //右对齐于组件
		elcp_relative_bottom_align_of = 8,                    //底对齐于组件
		elcp_relative_center_parent_h = 9,                    //水平居中于父
		elcp_relative_center_parent_v = 10,                   //垂直居中于父

		//绝对布局属性
		elcp_absolute_left = 1,                    //左侧
		elcp_absolute_left_type = 2,                    //位置类型_左侧
		elcp_absolute_top = 3,                    //顶部
		elcp_absolute_top_type = 4,                    //位置类型_顶部
		elcp_absolute_right = 5,                    //右侧
		elcp_absolute_right_type = 6,                    //位置类型_右侧
		elcp_absolute_bottom = 7,                    //底部
		elcp_absolute_bottom_type = 8,                    //位置类型_底部
		elcp_absolute_width = 9,                    //宽度（优先级低于右侧）
		elcp_absolute_width_type = 10,                   //位置类型_宽度
		elcp_absolute_height = 11,                   //高度（优先级低于底部）
		elcp_absolute_height_type = 12,                   //位置类型_高度
		elcp_absolute_offset_h = 13,                   //水平偏移量
		elcp_absolute_offset_h_type = 14,                   //位置类型_水平偏移量
		elcp_absolute_offset_v = 15,                   //垂直偏移量
		elcp_absolute_offset_v_type = 16,                   //位置类型_垂直偏移量

		//绝对布局位置类型
		elcp_absolute_type_unknown = 0,                    //未知 (未设置或保持不变)
		elcp_absolute_type_px = 1,                    //像素
		elcp_absolute_type_ps = 2,                    //百分比
		elcp_absolute_type_objps = 3,                    //组件尺寸百分比，仅OFFSET可用
		//布局事件
		ELN_GETPROPSCOUNT = 1,                    //获取布局父属性个数
		ELN_GETCHILDPROPCOUNT = 2,                    //获取布局子属性个数
		ELN_INITPROPS = 3,                    //初始化父属性列表
		ELN_UNINITPROPS = 4,                    //释放父属性列表
		ELN_INITCHILDPROPS = 5,                    //初始化子属性列表
		ELN_UNINITCHILDPROPS = 6,                    //释放子属性列表
		ELN_CHECKPROPVALUE = 7,                    //检查属性值是否正确,wParam为propID，lParam为值
		ELN_CHECKCHILDPROPVALUE = 8,                    //检查子属性值是否正确,wParam低位为nIndex，高位为propID，lParam为值
		ELN_UPDATE = 15,                   //更新布局
	};
	class TOAPI UILayout : public UIBase
	{
	public:
		UILayout() = default;
		~UILayout();
		/*
		 * @brief 初始化布局[创建后可直接操作布局类],一个父对象只能创建一个布局
		 * @param nType            [INT]            elt_ 布局类型
		*/
		BOOL Layout_Init(INT nType);
		//更新
		BOOL Layout_Update();
		//取布局类型
		INT Layout_GetType();
		//是否允许更新
		void Layout_SetEnableUpdate(BOOL fUpdateable);
		//分发通知
		LRESULT Layout_Notify(INT nEvent, WPARAM wParam, LPARAM lParam);

		/*表格布局相关 只能在表格局中使用*/
		//表格布局置信息
		BOOL Layout_Table_SetInfo(INT* aRowHeight, INT cRows, INT* aCellWidth, INT cCells);

		/*通用布局属性 任何布局类型都可以使用*/
		//置子属性
		BOOL Layout_SetChildProp(UIControl*parent, INT dwPropID, size_t pvValue);
		//取子属性
		BOOL Layout_GetChildProp(UIControl*parent, INT dwPropID, size_t* pvValue);
		//取子属性列表
		BOOL Layout_GetChildPropList(UIControl*parent, LPVOID* lpProps);
		//置属性
		BOOL Layout_SetProp(INT dwPropID, size_t pvValue);
		//取属性
		size_t Layout_GetProp(INT dwPropID);
		//取属性列表
		LPVOID Layout_GetPropList();

		/*绝对布局相关 只能在绝对布局中使用*/
		//绝对布局按当前位置锁定 仅支持0、默认 1、数值锁 2比例锁
		BOOL Layout_Absolute_Lock(UIControl*hObjChild, INT tLeft, INT tTop, INT tRight, INT tBottom, INT tWidth, INT tHeight);
		//绝对布局置边界信息
		BOOL Layout_Absolute_Setedge(UIControl*hObjChild, INT dwEdge/*elcp_absolute_开头*/, INT dwType/*elt_开头 布局类型*/, size_t nValue);

		/*特殊相关 只能在表格、线性、流式布局中使用*/
		//添加控件
		BOOL Layout_AddChild(UIControl*parent);
		/*
         * @brief 布局加入所有控件,已被加入的不会重复添加
         * @param 1  fDesc                   [BOOL]                是否倒序
         * @param 2  dwObjClass              [LPCWSTR]             0或空为所有
         * @param 3  nCount                  [INT*]                加入的个数
         * @return [BOOL]返回是否成功
         */
		BOOL Layout_AddChildren(BOOL fDesc, LPCWSTR dwObjClass, INT* nCount);
		//删除控件
		BOOL Layout_DeleteChild(UIControl*parent);
		//删除指定类名所有控件 dwObjClass为NULL为所有
		BOOL Layout_DeleteChildren(LPCWSTR dwObjClass = NULL);
		//取控件数量
		size_t Layout_GetChildCount();

	private:
		BOOL layout_bind(INT nType, UIBase* hObjBind);
		LRESULT layout_linear_proc(INT nEvent, WPARAM wParam, LPARAM lParam);
		LRESULT layout_flow_proc(INT nEvent, WPARAM wParam, LPARAM lParam);
		LRESULT layout_page_proc(INT nEvent, WPARAM wParam, LPARAM lParam);
		LRESULT layout_table_proc(INT nEvent, WPARAM wParam, LPARAM lParam);
		LRESULT layout_relative_proc(INT nEvent, WPARAM wParam, LPARAM lParam);
		LRESULT layout_absolute_proc(INT nEvent, WPARAM wParam, LPARAM lParam);
		void layout_move_margin(LPVOID parent, ExRectF& lpObjRc, LPVOID lpMargin, INT dwLockFlags, INT dwOrgFlags);
		void layout_relative_update(LPARAM lParam);
		LPVOID layout_get_child(LPVOID parent);
		void layout_free_info(LPVOID pvItem);
		struct LayoutData
		{
			LPVOID lpLayoutInfo = nullptr;
			LayoutPROC lpfnProc = 0;
			UIarray* hArrChildrenInfo = nullptr;
			INT nType = 0;	
			UIBase* hBind = nullptr;
			INT cbInfoLen = 0;	
			BOOL fUpdateable = 0;
		}l_data;
		friend class UIWnd;
		friend class UIControl;
	};
}


