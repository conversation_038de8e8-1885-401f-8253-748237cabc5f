/*
** 版权所有 （c） 2025 HHBUI ：https://hhbui.com/
** 保留所有权利.
** HHBUI 核心模块头文件 - 仅包含核心功能
*/

#pragma once

// 基础定义和配置
#include "application/define.h"
#include "application/config.h"

// 核心工具类
#include "common/vstring.hpp"
#include "common/assist.h"
#include "common/coordinate.h"

// 引擎核心
#include "engine/base.h"
#include "engine/engine.h"
#include "engine/matrix.h"

// 基础元素
#include "element/resource.h"
#include "element/color.h"
#include "element/font.h"

// 智能指针和内存管理
#include "common/auto_ptr.hpp"
#include "common/smart_ptr.hpp"
