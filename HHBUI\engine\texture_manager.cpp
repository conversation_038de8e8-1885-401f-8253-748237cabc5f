/**
** =====================================================================================
**
**       文件名称: texture_manager.cpp
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】纹理管理系统 - 高性能纹理压缩与流式加载框架 （实现文件）
**
** =====================================================================================
**/

#include "pch.h"
#include "texture_manager.h"
#include "common/Exception.h"
#include <algorithm>
#include <execution>

namespace HHBUI
{
    // 全局纹理管理器实例
    UITextureManager* g_texture_manager = nullptr;
    UIShaderCache* g_shader_cache = nullptr;

    // UITexture 实现
    UITexture::UITexture()
        : m_memory_usage(0)
        , m_is_loading(false)
    {
        memset(&m_desc, 0, sizeof(m_desc));
    }

    UITexture::~UITexture()
    {
        Release();
    }

    HRESULT UITexture::Create(ID3D11Device* device, const TextureDesc& desc, const void* initial_data)
    {
        try
        {
            if (!device)
                return E_INVALIDARG;

            m_desc = desc;

            D3D11_TEXTURE2D_DESC d3d_desc = {};
            d3d_desc.Width = desc.width;
            d3d_desc.Height = desc.height;
            d3d_desc.MipLevels = desc.mip_levels;
            d3d_desc.ArraySize = desc.array_size;
            d3d_desc.Format = static_cast<DXGI_FORMAT>(desc.format);
            d3d_desc.SampleDesc.Count = 1;
            d3d_desc.SampleDesc.Quality = 0;
            d3d_desc.Usage = D3D11_USAGE_DEFAULT;
            d3d_desc.BindFlags = desc.bind_flags;
            d3d_desc.CPUAccessFlags = desc.cpu_access_flags;
            d3d_desc.MiscFlags = desc.misc_flags;

            D3D11_SUBRESOURCE_DATA init_data = {};
            D3D11_SUBRESOURCE_DATA* p_init_data = nullptr;
            
            if (initial_data)
            {
                init_data.pSysMem = initial_data;
                init_data.SysMemPitch = desc.width * 4; // 假设RGBA格式
                init_data.SysMemSlicePitch = 0;
                p_init_data = &init_data;
            }

            throw_if_failed(
                device->CreateTexture2D(&d3d_desc, p_init_data, &m_texture),
                L"创建纹理失败"
            );

            throw_if_failed(CreateViews(device), L"创建纹理视图失败");

            m_memory_usage = CalculateMemoryUsage();
            return S_OK;
        }
        catch (const Exception& ex)
        {
            return ex.GetStatus();
        }
    }

    HRESULT UITexture::LoadFromFile(ID3D11Device* device, LPCWSTR file_path, bool generate_mips)
    {
        try
        {
            if (!device || !file_path)
                return E_INVALIDARG;

            m_is_loading = true;

            // 这里应该使用DirectXTex库或类似的库来加载纹理
            // 为了简化，我们创建一个简单的1x1白色纹理
            uint32_t white_pixel = 0xFFFFFFFF;
            
            TextureDesc desc;
            desc.width = 1;
            desc.height = 1;
            desc.mip_levels = 1;
            desc.array_size = 1;
            desc.format = TextureFormat::R8G8B8A8_UNORM;
            desc.bind_flags = D3D11_BIND_SHADER_RESOURCE;

            HRESULT hr = Create(device, desc, &white_pixel);
            m_is_loading = false;
            return hr;
        }
        catch (const Exception& ex)
        {
            m_is_loading = false;
            return ex.GetStatus();
        }
    }

    HRESULT UITexture::LoadFromMemory(ID3D11Device* device, const void* data, size_t data_size, bool generate_mips)
    {
        try
        {
            if (!device || !data || data_size == 0)
                return E_INVALIDARG;

            m_is_loading = true;

            // 这里应该解析内存中的图像数据
            // 为了简化，我们假设数据是原始RGBA像素数据
            TextureDesc desc;
            desc.width = static_cast<uint32_t>(sqrt(data_size / 4)); // 假设正方形纹理
            desc.height = desc.width;
            desc.mip_levels = generate_mips ? 0 : 1;
            desc.array_size = 1;
            desc.format = TextureFormat::R8G8B8A8_UNORM;
            desc.bind_flags = D3D11_BIND_SHADER_RESOURCE;
            if (generate_mips)
                desc.bind_flags |= D3D11_BIND_RENDER_TARGET;
            desc.misc_flags = generate_mips ? D3D11_RESOURCE_MISC_GENERATE_MIPS : 0;

            HRESULT hr = Create(device, desc, data);
            m_is_loading = false;
            return hr;
        }
        catch (const Exception& ex)
        {
            m_is_loading = false;
            return ex.GetStatus();
        }
    }

    HRESULT UITexture::CompressTexture(TextureFormat target_format)
    {
        // 纹理压缩实现
        // 这里需要使用DirectXTex库或类似的压缩算法
        return S_OK;
    }

    HRESULT UITexture::GenerateMipmaps(ID3D11DeviceContext* context)
    {
        if (!context || !m_srv)
            return E_FAIL;

        context->GenerateMips(m_srv.Get());
        return S_OK;
    }

    void UITexture::Release()
    {
        m_texture.Reset();
        m_srv.Reset();
        m_rtv.Reset();
        m_dsv.Reset();
        m_memory_usage = 0;
    }

    HRESULT UITexture::CreateViews(ID3D11Device* device)
    {
        try
        {
            if (!device || !m_texture)
                return E_FAIL;

            // 创建着色器资源视图
            if (m_desc.bind_flags & D3D11_BIND_SHADER_RESOURCE)
            {
                D3D11_SHADER_RESOURCE_VIEW_DESC srv_desc = {};
                srv_desc.Format = static_cast<DXGI_FORMAT>(m_desc.format);
                srv_desc.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE2D;
                srv_desc.Texture2D.MostDetailedMip = 0;
                srv_desc.Texture2D.MipLevels = m_desc.mip_levels;

                throw_if_failed(
                    device->CreateShaderResourceView(m_texture.Get(), &srv_desc, &m_srv),
                    L"创建着色器资源视图失败"
                );
            }

            // 创建渲染目标视图
            if (m_desc.bind_flags & D3D11_BIND_RENDER_TARGET)
            {
                D3D11_RENDER_TARGET_VIEW_DESC rtv_desc = {};
                rtv_desc.Format = static_cast<DXGI_FORMAT>(m_desc.format);
                rtv_desc.ViewDimension = D3D11_RTV_DIMENSION_TEXTURE2D;
                rtv_desc.Texture2D.MipSlice = 0;

                throw_if_failed(
                    device->CreateRenderTargetView(m_texture.Get(), &rtv_desc, &m_rtv),
                    L"创建渲染目标视图失败"
                );
            }

            // 创建深度模板视图
            if (m_desc.bind_flags & D3D11_BIND_DEPTH_STENCIL)
            {
                D3D11_DEPTH_STENCIL_VIEW_DESC dsv_desc = {};
                dsv_desc.Format = static_cast<DXGI_FORMAT>(m_desc.format);
                dsv_desc.ViewDimension = D3D11_DSV_DIMENSION_TEXTURE2D;
                dsv_desc.Texture2D.MipSlice = 0;

                throw_if_failed(
                    device->CreateDepthStencilView(m_texture.Get(), &dsv_desc, &m_dsv),
                    L"创建深度模板视图失败"
                );
            }

            return S_OK;
        }
        catch (const Exception& ex)
        {
            return ex.GetStatus();
        }
    }

    uint32_t UITexture::CalculateMemoryUsage() const
    {
        uint32_t bytes_per_pixel = 4; // 假设RGBA格式
        
        switch (m_desc.format)
        {
        case TextureFormat::R8G8B8A8_UNORM:
        case TextureFormat::R8G8B8A8_SRGB:
            bytes_per_pixel = 4;
            break;
        case TextureFormat::BC1_UNORM:
        case TextureFormat::BC1_SRGB:
            bytes_per_pixel = 1; // 压缩比8:1
            break;
        case TextureFormat::BC3_UNORM:
        case TextureFormat::BC3_SRGB:
            bytes_per_pixel = 1; // 压缩比4:1
            break;
        default:
            bytes_per_pixel = 4;
            break;
        }

        uint32_t total_size = 0;
        uint32_t width = m_desc.width;
        uint32_t height = m_desc.height;

        for (uint32_t mip = 0; mip < m_desc.mip_levels; ++mip)
        {
            total_size += width * height * bytes_per_pixel;
            width = std::max(1u, width / 2);
            height = std::max(1u, height / 2);
        }

        return total_size * m_desc.array_size;
    }

    // UIGPUMemoryPool 实现
    UIGPUMemoryPool::UIGPUMemoryPool()
        : m_device(nullptr)
        , m_stats{}
    {
    }

    UIGPUMemoryPool::~UIGPUMemoryPool()
    {
        Shutdown();
    }

    HRESULT UIGPUMemoryPool::Initialize(ID3D11Device* device, uint64_t pool_size)
    {
        if (!device)
            return E_INVALIDARG;

        m_device = device;
        m_stats.total_size = pool_size;
        m_stats.free_size = pool_size;

        // 创建初始内存块
        MemoryBlock initial_block;
        initial_block.offset = 0;
        initial_block.size = pool_size;
        initial_block.is_free = true;
        initial_block.texture = nullptr;

        m_blocks.push_back(initial_block);
        return S_OK;
    }

    void UIGPUMemoryPool::Shutdown()
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_blocks.clear();
        m_device = nullptr;
        memset(&m_stats, 0, sizeof(m_stats));
    }

    HRESULT UIGPUMemoryPool::AllocateTexture(const TextureDesc& desc, ID3D11Texture2D** texture)
    {
        std::lock_guard<std::mutex> lock(m_mutex);

        if (!m_device || !texture)
            return E_INVALIDARG;

        // 计算所需大小
        uint64_t required_size = desc.width * desc.height * 4; // 简化计算

        // 查找合适的空闲块
        MemoryBlock* block = FindFreeBlock(required_size);
        if (!block)
        {
            GarbageCollect();
            block = FindFreeBlock(required_size);
            if (!block)
                return E_OUTOFMEMORY;
        }

        // 分配纹理
        D3D11_TEXTURE2D_DESC d3d_desc = {};
        d3d_desc.Width = desc.width;
        d3d_desc.Height = desc.height;
        d3d_desc.MipLevels = desc.mip_levels;
        d3d_desc.ArraySize = desc.array_size;
        d3d_desc.Format = static_cast<DXGI_FORMAT>(desc.format);
        d3d_desc.SampleDesc.Count = 1;
        d3d_desc.Usage = D3D11_USAGE_DEFAULT;
        d3d_desc.BindFlags = desc.bind_flags;

        HRESULT hr = m_device->CreateTexture2D(&d3d_desc, nullptr, texture);
        if (SUCCEEDED(hr))
        {
            block->is_free = false;
            block->texture = *texture;
            m_stats.used_size += required_size;
            m_stats.free_size -= required_size;
            m_stats.allocation_count++;
        }

        return hr;
    }

    void UIGPUMemoryPool::DeallocateTexture(ID3D11Texture2D* texture)
    {
        std::lock_guard<std::mutex> lock(m_mutex);

        for (auto& block : m_blocks)
        {
            if (block.texture == texture)
            {
                block.is_free = true;
                block.texture = nullptr;
                m_stats.used_size -= block.size;
                m_stats.free_size += block.size;
                m_stats.deallocation_count++;
                break;
            }
        }

        CoalesceBlocks();
    }

    void UIGPUMemoryPool::GarbageCollect()
    {
        CoalesceBlocks();
        
        // 计算碎片率
        uint64_t largest_free_block = 0;
        for (const auto& block : m_blocks)
        {
            if (block.is_free && block.size > largest_free_block)
            {
                largest_free_block = block.size;
            }
        }

        m_stats.fragmentation_ratio = 1.0f - static_cast<float>(largest_free_block) / m_stats.free_size;
    }

    void UIGPUMemoryPool::CoalesceBlocks()
    {
        bool merged = true;
        while (merged)
        {
            merged = false;
            for (size_t i = 0; i < m_blocks.size() - 1; ++i)
            {
                if (m_blocks[i].is_free && m_blocks[i + 1].is_free &&
                    m_blocks[i].offset + m_blocks[i].size == m_blocks[i + 1].offset)
                {
                    m_blocks[i].size += m_blocks[i + 1].size;
                    m_blocks.erase(m_blocks.begin() + i + 1);
                    merged = true;
                    break;
                }
            }
        }
    }

    UIGPUMemoryPool::MemoryBlock* UIGPUMemoryPool::FindFreeBlock(uint64_t size)
    {
        for (auto& block : m_blocks)
        {
            if (block.is_free && block.size >= size)
            {
                // 如果块太大，分割它
                if (block.size > size * 2)
                {
                    MemoryBlock new_block;
                    new_block.offset = block.offset + size;
                    new_block.size = block.size - size;
                    new_block.is_free = true;
                    new_block.texture = nullptr;

                    block.size = size;
                    m_blocks.insert(m_blocks.begin() + (&block - &m_blocks[0]) + 1, new_block);
                }
                return &block;
            }
        }
        return nullptr;
    }
}
