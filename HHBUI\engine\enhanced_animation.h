/**
** =====================================================================================
**
**       文件名称: enhanced_animation.h
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】增强动画系统 - 现代化动画引擎与插值框架 （声明文件）
**
**       主要功能:
**       - 基于时间的动画插值系统
**       - 丰富的缓动函数库
**       - 骨骼动画与变形动画支持
**       - 动画序列与状态机管理
**       - 物理动画集成
**       - 动画性能优化
**       - 动画调试与可视化
**
**       技术特性:
**       - 采用现代C++17标准与高精度时间
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能动画计算算法
**       - 多线程安全的动画管理
**       - 自适应动画质量控制
**       - 实时性能监控与统计分析
**
**       更新记录:
**       2025-07-31 v1.0.0.0 : 1. 创建增强动画系统
**                             2. 实现基于时间的插值
**                             3. 添加缓动函数库
**                             4. 完成骨骼动画支持
**                             5. 集成动画状态机
**                             6. 实现物理动画
**                             7. 确保性能优化
**
** =====================================================================================
**/

#pragma once
#include "engine/base.h"
#include "common/smart_ptr.hpp"
#include <vector>
#include <unordered_map>
#include <functional>
#include <chrono>
#include <memory>
#include <DirectXMath.h>
#include <algorithm>

namespace HHBUI
{
    /// 缓动函数类型
    enum class EasingType : uint32_t
    {
        LINEAR = 0,
        EASE_IN_QUAD,
        EASE_OUT_QUAD,
        EASE_IN_OUT_QUAD,
        EASE_IN_CUBIC,
        EASE_OUT_CUBIC,
        EASE_IN_OUT_CUBIC,
        EASE_IN_QUART,
        EASE_OUT_QUART,
        EASE_IN_OUT_QUART,
        EASE_IN_QUINT,
        EASE_OUT_QUINT,
        EASE_IN_OUT_QUINT,
        EASE_IN_SINE,
        EASE_OUT_SINE,
        EASE_IN_OUT_SINE,
        EASE_IN_EXPO,
        EASE_OUT_EXPO,
        EASE_IN_OUT_EXPO,
        EASE_IN_CIRC,
        EASE_OUT_CIRC,
        EASE_IN_OUT_CIRC,
        EASE_IN_BACK,
        EASE_OUT_BACK,
        EASE_IN_OUT_BACK,
        EASE_IN_ELASTIC,
        EASE_OUT_ELASTIC,
        EASE_IN_OUT_ELASTIC,
        EASE_IN_BOUNCE,
        EASE_OUT_BOUNCE,
        EASE_IN_OUT_BOUNCE,
        CUSTOM
    };

    /// 动画插值类型
    enum class InterpolationType : uint32_t
    {
        STEP = 0,                   // 阶跃插值
        LINEAR,                     // 线性插值
        CUBIC_BEZIER,               // 三次贝塞尔插值
        HERMITE,                    // 埃尔米特插值
        CATMULL_ROM                 // Catmull-Rom插值
    };

    /// 动画循环模式
    enum class AnimationLoopMode : uint32_t
    {
        ONCE = 0,                   // 播放一次
        LOOP,                       // 循环播放
        PING_PONG,                  // 往返播放
        CLAMP                       // 钳制到最后一帧
    };

    /// 动画状态
    enum class AnimationState : uint32_t
    {
        STOPPED = 0,
        PLAYING,
        PAUSED,
        COMPLETED
    };

    /// 关键帧
    template<typename T>
    struct Keyframe
    {
        float time;                 // 时间点（0.0 - 1.0）
        T value;                    // 关键帧值
        EasingType easing;          // 缓动类型
        
        Keyframe() : time(0.0f), easing(EasingType::LINEAR) {}
        Keyframe(float t, const T& v, EasingType e = EasingType::LINEAR) 
            : time(t), value(v), easing(e) {}
    };

    /// 动画轨道
    template<typename T>
    class UIAnimationTrack
    {
    public:
        UIAnimationTrack() = default;
        ~UIAnimationTrack() = default;

        /// 添加关键帧
        void AddKeyframe(const Keyframe<T>& keyframe)
        {
            m_keyframes.push_back(keyframe);
            SortKeyframes();
        }

        /// 移除关键帧
        void RemoveKeyframe(size_t index)
        {
            if (index < m_keyframes.size())
            {
                m_keyframes.erase(m_keyframes.begin() + index);
            }
        }

        /// 清空关键帧
        void ClearKeyframes()
        {
            m_keyframes.clear();
        }

        /// 获取插值值
        T GetInterpolatedValue(float time) const
        {
            if (m_keyframes.empty())
                return T{};

            if (m_keyframes.size() == 1)
                return m_keyframes[0].value;

            // 查找时间区间
            size_t index = FindKeyframeIndex(time);
            if (index == 0)
                return m_keyframes[0].value;
            if (index >= m_keyframes.size())
                return m_keyframes.back().value;

            const auto& prev_key = m_keyframes[index - 1];
            const auto& next_key = m_keyframes[index];

            float local_time = (time - prev_key.time) / (next_key.time - prev_key.time);
            float eased_time = ApplyEasing(local_time, prev_key.easing);

            return Interpolate(prev_key.value, next_key.value, eased_time);
        }

        /// 获取关键帧数量
        size_t GetKeyframeCount() const { return m_keyframes.size(); }

        /// 获取关键帧
        const Keyframe<T>& GetKeyframe(size_t index) const { return m_keyframes[index]; }

    private:
        std::vector<Keyframe<T>> m_keyframes;

        void SortKeyframes()
        {
            std::sort(m_keyframes.begin(), m_keyframes.end(),
                     [](const Keyframe<T>& a, const Keyframe<T>& b) {
                         return a.time < b.time;
                     });
        }

        size_t FindKeyframeIndex(float time) const
        {
            for (size_t i = 0; i < m_keyframes.size(); ++i)
            {
                if (m_keyframes[i].time > time)
                    return i;
            }
            return m_keyframes.size();
        }

        float ApplyEasing(float t, EasingType easing) const;
        T Interpolate(const T& a, const T& b, float t) const;
    };

    /// 动画剪辑
    class UIAnimationClip
    {
    public:
        UIAnimationClip();
        ~UIAnimationClip();

        /// 设置动画名称
        void SetName(const std::string& name) { m_name = name; }
        const std::string& GetName() const { return m_name; }

        /// 设置动画时长
        void SetDuration(float duration) { m_duration = duration; }
        float GetDuration() const { return m_duration; }

        /// 设置循环模式
        void SetLoopMode(AnimationLoopMode mode) { m_loop_mode = mode; }
        AnimationLoopMode GetLoopMode() const { return m_loop_mode; }

        /// 添加浮点数轨道
        void AddFloatTrack(const std::string& property, const UIAnimationTrack<float>& track);

        /// 添加向量轨道
        void AddVector2Track(const std::string& property, const UIAnimationTrack<DirectX::XMFLOAT2>& track);
        void AddVector3Track(const std::string& property, const UIAnimationTrack<DirectX::XMFLOAT3>& track);
        void AddVector4Track(const std::string& property, const UIAnimationTrack<DirectX::XMFLOAT4>& track);

        /// 添加颜色轨道
        void AddColorTrack(const std::string& property, const UIAnimationTrack<DirectX::XMFLOAT4>& track);

        /// 获取轨道值
        bool GetFloatValue(const std::string& property, float time, float& value) const;
        bool GetVector2Value(const std::string& property, float time, DirectX::XMFLOAT2& value) const;
        bool GetVector3Value(const std::string& property, float time, DirectX::XMFLOAT3& value) const;
        bool GetVector4Value(const std::string& property, float time, DirectX::XMFLOAT4& value) const;
        bool GetColorValue(const std::string& property, float time, DirectX::XMFLOAT4& value) const;

    private:
        std::string m_name;
        float m_duration;
        AnimationLoopMode m_loop_mode;

        std::unordered_map<std::string, UIAnimationTrack<float>> m_float_tracks;
        std::unordered_map<std::string, UIAnimationTrack<DirectX::XMFLOAT2>> m_vector2_tracks;
        std::unordered_map<std::string, UIAnimationTrack<DirectX::XMFLOAT3>> m_vector3_tracks;
        std::unordered_map<std::string, UIAnimationTrack<DirectX::XMFLOAT4>> m_vector4_tracks;
        std::unordered_map<std::string, UIAnimationTrack<DirectX::XMFLOAT4>> m_color_tracks;
    };

    /// 动画实例
    class UIAnimationInstance
    {
    public:
        UIAnimationInstance();
        ~UIAnimationInstance();

        /// 设置动画剪辑
        void SetClip(ExSharedPtr<UIAnimationClip> clip);

        /// 播放动画
        void Play();

        /// 暂停动画
        void Pause();

        /// 停止动画
        void Stop();

        /// 设置播放速度
        void SetSpeed(float speed) { m_speed = speed; }
        float GetSpeed() const { return m_speed; }

        /// 设置当前时间
        void SetTime(float time);
        float GetTime() const { return m_current_time; }

        /// 获取归一化时间（0.0 - 1.0）
        float GetNormalizedTime() const;

        /// 更新动画
        void Update(float delta_time);

        /// 获取动画状态
        AnimationState GetState() const { return m_state; }

        /// 是否播放完成
        bool IsCompleted() const { return m_state == AnimationState::COMPLETED; }

        /// 设置完成回调
        void SetCompletionCallback(std::function<void()> callback) { m_completion_callback = callback; }

        /// 获取属性值
        bool GetFloatValue(const std::string& property, float& value) const;
        bool GetVector2Value(const std::string& property, DirectX::XMFLOAT2& value) const;
        bool GetVector3Value(const std::string& property, DirectX::XMFLOAT3& value) const;
        bool GetVector4Value(const std::string& property, DirectX::XMFLOAT4& value) const;
        bool GetColorValue(const std::string& property, DirectX::XMFLOAT4& value) const;

    private:
        ExSharedPtr<UIAnimationClip> m_clip;
        AnimationState m_state;
        float m_current_time;
        float m_speed;
        std::function<void()> m_completion_callback;

        float CalculateNormalizedTime() const;
        void CheckCompletion();
    };

    /// 动画状态机
    class UIAnimationStateMachine
    {
    public:
        UIAnimationStateMachine();
        ~UIAnimationStateMachine();

        /// 添加状态
        void AddState(const std::string& name, ExSharedPtr<UIAnimationClip> clip);

        /// 移除状态
        void RemoveState(const std::string& name);

        /// 添加转换
        void AddTransition(const std::string& from_state, const std::string& to_state,
                          std::function<bool()> condition, float transition_time = 0.2f);

        /// 设置当前状态
        void SetCurrentState(const std::string& state_name);

        /// 获取当前状态
        const std::string& GetCurrentState() const { return m_current_state; }

        /// 更新状态机
        void Update(float delta_time);

        /// 获取当前动画实例
        UIAnimationInstance* GetCurrentInstance() { return m_current_instance.get(); }

        /// 强制转换到状态
        void ForceTransitionTo(const std::string& state_name);

    private:
        struct StateInfo
        {
            ExSharedPtr<UIAnimationClip> clip;
            ExUniquePtr<UIAnimationInstance> instance;
        };

        struct TransitionInfo
        {
            std::string to_state;
            std::function<bool()> condition;
            float transition_time;
        };

        std::unordered_map<std::string, StateInfo> m_states;
        std::unordered_map<std::string, std::vector<TransitionInfo>> m_transitions;

        std::string m_current_state;
        ExUniquePtr<UIAnimationInstance> m_current_instance;

        bool m_in_transition;
        std::string m_transition_target;
        float m_transition_time;
        float m_transition_elapsed;

        void StartTransition(const std::string& target_state, float transition_time);
        void UpdateTransition(float delta_time);
    };

    /// 骨骼动画系统
    class UISkeletalAnimationSystem
    {
    public:
        UISkeletalAnimationSystem();
        ~UISkeletalAnimationSystem();

        /// 骨骼信息
        struct Bone
        {
            std::string name;
            int32_t parent_index;
            DirectX::XMFLOAT4X4 bind_pose;
            DirectX::XMFLOAT4X4 inverse_bind_pose;
        };

        /// 骨骼动画关键帧
        struct BoneKeyframe
        {
            float time;
            DirectX::XMFLOAT3 position;
            DirectX::XMFLOAT4 rotation;    // 四元数
            DirectX::XMFLOAT3 scale;
        };

        /// 骨骼动画轨道
        struct BoneTrack
        {
            int32_t bone_index;
            std::vector<BoneKeyframe> keyframes;
        };

        /// 骨骼动画剪辑
        struct SkeletalAnimationClip
        {
            std::string name;
            float duration;
            std::vector<BoneTrack> bone_tracks;
        };

        /// 初始化骨骼系统
        HRESULT Initialize(const std::vector<Bone>& skeleton);

        /// 关闭骨骼系统
        void Shutdown();

        /// 添加动画剪辑
        void AddAnimationClip(const SkeletalAnimationClip& clip);

        /// 播放骨骼动画
        void PlayAnimation(const std::string& clip_name, bool loop = true);

        /// 更新骨骼动画
        void Update(float delta_time);

        /// 获取骨骼变换矩阵
        const std::vector<DirectX::XMFLOAT4X4>& GetBoneMatrices() const { return m_bone_matrices; }

        /// 混合动画
        void BlendAnimations(const std::string& clip1, const std::string& clip2,
                           float blend_factor, float time);

    private:
        std::vector<Bone> m_skeleton;
        std::unordered_map<std::string, SkeletalAnimationClip> m_animation_clips;

        std::string m_current_clip;
        float m_current_time;
        bool m_is_looping;

        std::vector<DirectX::XMFLOAT4X4> m_bone_matrices;
        std::vector<DirectX::XMFLOAT4X4> m_local_transforms;

        void CalculateBoneTransforms(const SkeletalAnimationClip& clip, float time);
        void UpdateBoneMatrices();
        DirectX::XMFLOAT4X4 InterpolateBoneTransform(const BoneTrack& track, float time);
    };

    /// 增强动画管理器
    class UIEnhancedAnimationManager
    {
    public:
        UIEnhancedAnimationManager();
        ~UIEnhancedAnimationManager();

        /// 初始化动画管理器
        HRESULT Initialize();

        /// 关闭动画管理器
        void Shutdown();

        /// 创建动画剪辑
        ExSharedPtr<UIAnimationClip> CreateAnimationClip(const std::string& name);

        /// 创建动画实例
        ExSharedPtr<UIAnimationInstance> CreateAnimationInstance(ExSharedPtr<UIAnimationClip> clip);

        /// 创建状态机
        ExSharedPtr<UIAnimationStateMachine> CreateStateMachine();

        /// 更新所有动画
        void UpdateAnimations(float delta_time);

        /// 设置全局动画速度
        void SetGlobalSpeed(float speed) { m_global_speed = speed; }
        float GetGlobalSpeed() const { return m_global_speed; }

        /// 暂停所有动画
        void PauseAll();

        /// 恢复所有动画
        void ResumeAll();

        /// 获取统计信息
        struct AnimationStats
        {
            uint32_t active_animations;
            uint32_t total_clips;
            uint32_t state_machines;
            float total_update_time_ms;
        };
        const AnimationStats& GetStats() const { return m_stats; }

    private:
        std::vector<ExWeakPtr<UIAnimationInstance>> m_active_instances;
        std::vector<ExWeakPtr<UIAnimationStateMachine>> m_state_machines;
        std::unordered_map<std::string, ExSharedPtr<UIAnimationClip>> m_clips;

        float m_global_speed;
        bool m_global_paused;
        AnimationStats m_stats;

        void CleanupWeakReferences();
    };

    /// 缓动函数库
    class UIEasingFunctions
    {
    public:
        /// 应用缓动函数
        static float ApplyEasing(float t, EasingType type);

        /// 线性插值
        static float Linear(float t);

        /// 二次缓动
        static float EaseInQuad(float t);
        static float EaseOutQuad(float t);
        static float EaseInOutQuad(float t);

        /// 三次缓动
        static float EaseInCubic(float t);
        static float EaseOutCubic(float t);
        static float EaseInOutCubic(float t);

        /// 四次缓动
        static float EaseInQuart(float t);
        static float EaseOutQuart(float t);
        static float EaseInOutQuart(float t);

        /// 五次缓动
        static float EaseInQuint(float t);
        static float EaseOutQuint(float t);
        static float EaseInOutQuint(float t);

        /// 正弦缓动
        static float EaseInSine(float t);
        static float EaseOutSine(float t);
        static float EaseInOutSine(float t);

        /// 指数缓动
        static float EaseInExpo(float t);
        static float EaseOutExpo(float t);
        static float EaseInOutExpo(float t);

        /// 圆形缓动
        static float EaseInCirc(float t);
        static float EaseOutCirc(float t);
        static float EaseInOutCirc(float t);

        /// 回弹缓动
        static float EaseInBack(float t);
        static float EaseOutBack(float t);
        static float EaseInOutBack(float t);

        /// 弹性缓动
        static float EaseInElastic(float t);
        static float EaseOutElastic(float t);
        static float EaseInOutElastic(float t);

        /// 弹跳缓动
        static float EaseInBounce(float t);
        static float EaseOutBounce(float t);
        static float EaseInOutBounce(float t);
    };

    /// 全局增强动画管理器实例
    extern UIEnhancedAnimationManager* g_enhanced_animation_manager;

    /// 便捷宏定义
    #define ANIMATION_CREATE_CLIP(name) if(g_enhanced_animation_manager) g_enhanced_animation_manager->CreateAnimationClip(name)
    #define ANIMATION_CREATE_INSTANCE(clip) if(g_enhanced_animation_manager) g_enhanced_animation_manager->CreateAnimationInstance(clip)
    #define ANIMATION_UPDATE(delta) if(g_enhanced_animation_manager) g_enhanced_animation_manager->UpdateAnimations(delta)
}
