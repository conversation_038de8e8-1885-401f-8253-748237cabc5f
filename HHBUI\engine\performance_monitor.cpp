/**
** =====================================================================================
**
**       文件名称: performance_monitor.cpp
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】性能监控系统 - 实时性能分析与自动优化框架 （实现文件）
**
** =====================================================================================
**/

#include "pch.h"

// 防止Windows宏干扰
#ifndef NOMINMAX
#define NOMINMAX
#endif

// 确保使用函数版本而不是宏版本
#ifdef min
#undef min
#endif
#ifdef max
#undef max
#endif

#include "performance_monitor.h"
#include <fstream>
#include <sstream>
#include <iomanip>

namespace HHBUI
{
    // 全局性能监控系统实例
    UIPerformanceMonitor* g_performance_monitor = nullptr;
    UIMemoryAnalyzer* g_memory_analyzer = nullptr;
    UIFrameRateMonitor* g_framerate_monitor = nullptr;
    UIAutoOptimizationManager* g_auto_optimization_manager = nullptr;
    UIPerformancePanel* g_performance_panel = nullptr;

    // UIPerformanceMonitor 实现
    UIPerformanceMonitor::UIPerformanceMonitor()
        : m_history_size(1000)
        , m_enabled(true)
    {
    }

    UIPerformanceMonitor::~UIPerformanceMonitor()
    {
        Shutdown();
    }

    HRESULT UIPerformanceMonitor::Initialize(uint32_t history_size)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        m_history_size = history_size;
        m_enabled = true;
        
        // 初始化所有性能指标
        for (int i = 0; i < static_cast<int>(PerformanceMetric::CUSTOM); ++i)
        {
            PerformanceMetric metric = static_cast<PerformanceMetric>(i);
            MetricData& data = m_metrics[metric];
            data.history.reserve(history_size);
            data.history_index = 0;
            
            // 设置默认阈值
            switch (metric)
            {
            case PerformanceMetric::FRAME_TIME:
                data.threshold = PerformanceThreshold(16.67f, 33.33f, false); // 60fps, 30fps
                break;
            case PerformanceMetric::FPS:
                data.threshold = PerformanceThreshold(30.0f, 15.0f, true);
                break;
            case PerformanceMetric::MEMORY_USAGE:
                data.threshold = PerformanceThreshold(512 * 1024 * 1024, 1024 * 1024 * 1024, false);
                break;
            default:
                data.threshold = PerformanceThreshold(0.8f, 0.9f, false);
                break;
            }
        }
        
        return S_OK;
    }

    void UIPerformanceMonitor::Shutdown()
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_metrics.clear();
        m_enabled = false;
    }

    void UIPerformanceMonitor::BeginFrame()
    {
        if (!m_enabled)
            return;
            
        m_frame_start_time = std::chrono::steady_clock::now();
    }

    void UIPerformanceMonitor::EndFrame()
    {
        if (!m_enabled)
            return;
            
        auto frame_end_time = std::chrono::steady_clock::now();
        auto frame_duration = std::chrono::duration_cast<std::chrono::microseconds>(frame_end_time - m_frame_start_time);
        float frame_time_ms = frame_duration.count() / 1000.0f;
        
        RecordMetric(PerformanceMetric::FRAME_TIME, frame_time_ms);
        RecordMetric(PerformanceMetric::FPS, 1000.0f / frame_time_ms);
    }

    void UIPerformanceMonitor::RecordMetric(PerformanceMetric metric, float value)
    {
        if (!m_enabled)
            return;
            
        std::lock_guard<std::mutex> lock(m_mutex);

        auto it = m_metrics.find(metric);
        if (it != m_metrics.end())
        {
            AddDataPoint(it->second, value);
            UpdateStats(it->second);
        }
    }

    void UIPerformanceMonitor::SetThreshold(PerformanceMetric metric, const PerformanceThreshold& threshold)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_metrics[metric].threshold = threshold;
    }

    const PerformanceStats& UIPerformanceMonitor::GetStats(PerformanceMetric metric) const
    {
        static PerformanceStats empty_stats;

        std::lock_guard<std::mutex> lock(m_mutex);
        auto it = m_metrics.find(metric);
        return (it != m_metrics.end()) ? it->second.stats : empty_stats;
    }

    const std::vector<PerformanceDataPoint>& UIPerformanceMonitor::GetHistory(PerformanceMetric metric) const
    {
        static std::vector<PerformanceDataPoint> empty_history;

        std::lock_guard<std::mutex> lock(m_mutex);
        auto it = m_metrics.find(metric);
        return (it != m_metrics.end()) ? it->second.history : empty_history;
    }

    PerformanceWarningLevel UIPerformanceMonitor::CheckWarningLevel(PerformanceMetric metric) const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        auto it = m_metrics.find(metric);
        if (it == m_metrics.end())
            return PerformanceWarningLevel::INFO;
            
        const auto& data = it->second;
        const auto& threshold = data.threshold;
        float current_value = data.stats.current_value;
        
        if (threshold.higher_is_better)
        {
            if (current_value < threshold.critical_threshold)
                return PerformanceWarningLevel::CRITICAL;
            else if (current_value < threshold.warning_threshold)
                return PerformanceWarningLevel::WARNING;
        }
        else
        {
            if (current_value > threshold.critical_threshold)
                return PerformanceWarningLevel::CRITICAL;
            else if (current_value > threshold.warning_threshold)
                return PerformanceWarningLevel::WARNING;
        }
        
        return PerformanceWarningLevel::INFO;
    }

    std::string UIPerformanceMonitor::GeneratePerformanceReport() const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::ostringstream report;
        report << "=== HHBUI Performance Report ===\n";
        report << "Generated at: " << std::chrono::duration_cast<std::chrono::seconds>(
            std::chrono::steady_clock::now().time_since_epoch()).count() << "\n\n";
        
        for (const auto& [metric, data] : m_metrics)
        {
            if (data.stats.sample_count == 0)
                continue;
                
            report << "Metric: " << static_cast<int>(metric) << "\n";
            report << "  Current: " << std::fixed << std::setprecision(2) << data.stats.current_value << "\n";
            report << "  Average: " << data.stats.average_value << "\n";
            report << "  Min: " << data.stats.min_value << "\n";
            report << "  Max: " << data.stats.max_value << "\n";
            report << "  Variance: " << data.stats.variance << "\n";
            report << "  Samples: " << data.stats.sample_count << "\n";
            
            PerformanceWarningLevel level = CheckWarningLevel(metric);
            report << "  Warning Level: ";
            switch (level)
            {
            case PerformanceWarningLevel::INFO: report << "INFO"; break;
            case PerformanceWarningLevel::WARNING: report << "WARNING"; break;
            case PerformanceWarningLevel::CRITICAL: report << "CRITICAL"; break;
            }
            report << "\n\n";
        }
        
        return report.str();
    }

    HRESULT UIPerformanceMonitor::ExportPerformanceData(LPCWSTR file_path) const
    {
        try
        {
            std::wofstream file(file_path);
            if (!file.is_open())
                return E_FAIL;
                
            std::string report = GeneratePerformanceReport();
            file << std::wstring(report.begin(), report.end());
            file.close();
            
            return S_OK;
        }
        catch (...)
        {
            return E_FAIL;
        }
    }

    void UIPerformanceMonitor::SetHistorySize(uint32_t size)
    {
        std::lock_guard<std::mutex> lock(m_mutex);

        m_history_size = size;
        for (auto& [metric, data] : m_metrics)
        {
            if (data.history.size() > size)
            {
                data.history.resize(size);
            }
            data.history.reserve(size);
        }
    }

    void UIPerformanceMonitor::ClearHistory()
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        for (auto& [metric, data] : m_metrics)
        {
            data.history.clear();
            data.history_index = 0;
            data.stats = PerformanceStats{};
        }
    }

    void UIPerformanceMonitor::UpdateStats(MetricData& data)
    {
        if (data.history.empty())
            return;
            
        // 计算统计信息
        data.stats.current_value = data.history.back().value;
        data.stats.sample_count = static_cast<uint32_t>(data.history.size());
        
        // 计算平均值、最小值、最大值
        float sum = 0.0f;
        data.stats.min_value = FLT_MAX;
        data.stats.max_value = -FLT_MAX;
        
        for (const auto& point : data.history)
        {
            sum += point.value;
            data.stats.min_value = (std::min)(data.stats.min_value, point.value);
            data.stats.max_value = (std::max)(data.stats.max_value, point.value);
        }
        
        data.stats.average_value = sum / data.stats.sample_count;
        
        // 计算方差
        float variance_sum = 0.0f;
        for (const auto& point : data.history)
        {
            float diff = point.value - data.stats.average_value;
            variance_sum += diff * diff;
        }
        data.stats.variance = variance_sum / data.stats.sample_count;
    }

    void UIPerformanceMonitor::AddDataPoint(MetricData& data, float value)
    {
        PerformanceDataPoint point(value);
        
        if (data.history.size() < m_history_size)
        {
            data.history.push_back(point);
        }
        else
        {
            data.history[data.history_index] = point;
            data.history_index = (data.history_index + 1) % m_history_size;
        }
    }

    // UIMemoryAnalyzer 实现
    UIMemoryAnalyzer::UIMemoryAnalyzer()
        : m_memory_budget(1024 * 1024 * 1024) // 1GB默认预算
        , m_stats{}
    {
    }

    UIMemoryAnalyzer::~UIMemoryAnalyzer()
    {
        Shutdown();
    }

    HRESULT UIMemoryAnalyzer::Initialize()
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        memset(&m_stats, 0, sizeof(m_stats));
        return S_OK;
    }

    void UIMemoryAnalyzer::Shutdown()
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_allocations.clear();
        memset(&m_stats, 0, sizeof(m_stats));
    }

    void UIMemoryAnalyzer::RecordAllocation(const std::string& category, size_t size, const void* ptr)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        AllocationInfo info;
        info.category = category;
        info.size = size;
        info.timestamp = std::chrono::steady_clock::now();
        
        m_allocations[ptr] = info;
        
        m_stats.total_allocated += size;
        m_stats.current_usage += size;
        m_stats.allocation_count++;
        m_stats.active_allocations++;
        
        if (m_stats.current_usage > m_stats.peak_usage)
        {
            m_stats.peak_usage = m_stats.current_usage;
        }
        
        UpdateCategoryUsage(category, static_cast<int64_t>(size));
    }

    void UIMemoryAnalyzer::RecordDeallocation(const void* ptr)
    {
        std::lock_guard<std::mutex> lock(m_mutex);

        auto it = m_allocations.find(ptr);
        if (it != m_allocations.end())
        {
            const AllocationInfo& info = it->second;

            m_stats.total_freed += info.size;
            m_stats.current_usage -= info.size;
            m_stats.deallocation_count++;
            m_stats.active_allocations--;

            UpdateCategoryUsage(info.category, -static_cast<int64_t>(info.size));

            m_allocations.erase(it);
        }
    }

    std::vector<std::pair<const void*, size_t>> UIMemoryAnalyzer::DetectMemoryLeaks() const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::vector<std::pair<const void*, size_t>> leaks;
        for (const auto& [ptr, info] : m_allocations)
        {
            leaks.emplace_back(ptr, info.size);
        }
        
        return leaks;
    }

    std::string UIMemoryAnalyzer::GenerateMemoryReport() const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        std::ostringstream report;
        report << "=== HHBUI Memory Report ===\n";
        report << "Total Allocated: " << (m_stats.total_allocated / 1024 / 1024) << " MB\n";
        report << "Total Freed: " << (m_stats.total_freed / 1024 / 1024) << " MB\n";
        report << "Current Usage: " << (m_stats.current_usage / 1024 / 1024) << " MB\n";
        report << "Peak Usage: " << (m_stats.peak_usage / 1024 / 1024) << " MB\n";
        report << "Active Allocations: " << m_stats.active_allocations << "\n";
        report << "Memory Budget: " << (m_memory_budget / 1024 / 1024) << " MB\n";
        report << "Budget Exceeded: " << (IsMemoryBudgetExceeded() ? "YES" : "NO") << "\n\n";
        
        report << "Category Usage:\n";
        for (const auto& [category, usage] : m_stats.category_usage)
        {
            report << "  " << category << ": " << (usage / 1024 / 1024) << " MB\n";
        }
        
        return report.str();
    }

    void UIMemoryAnalyzer::UpdateCategoryUsage(const std::string& category, int64_t size_delta)
    {
        auto it = m_stats.category_usage.find(category);
        if (it != m_stats.category_usage.end())
        {
            it->second += size_delta;
            if (it->second <= 0)
            {
                m_stats.category_usage.erase(it);
            }
        }
        else if (size_delta > 0)
        {
            m_stats.category_usage[category] = size_delta;
        }
    }

    // UIFrameRateMonitor 实现
    UIFrameRateMonitor::UIFrameRateMonitor()
        : m_sample_window(60)
        , m_current_index(0)
        , m_current_fps(0.0f)
        , m_average_fps(0.0f)
        , m_frame_time_variance(0.0f)
        , m_stats{}
    {
    }

    UIFrameRateMonitor::~UIFrameRateMonitor()
    {
        Shutdown();
    }

    HRESULT UIFrameRateMonitor::Initialize(uint32_t sample_window)
    {
        std::lock_guard<std::mutex> lock(m_mutex);

        m_sample_window = sample_window;
        m_frame_times.resize(sample_window, 0.0f);
        m_current_index = 0;

        memset(&m_stats, 0, sizeof(m_stats));
        m_stats.min_fps = FLT_MAX;
        m_stats.max_fps = -FLT_MAX;

        return S_OK;
    }

    void UIFrameRateMonitor::Shutdown()
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_frame_times.clear();
        memset(&m_stats, 0, sizeof(m_stats));
    }

    void UIFrameRateMonitor::RecordFrameTime(float frame_time_ms)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        m_frame_times[m_current_index] = frame_time_ms;
        m_current_index = (m_current_index + 1) % m_sample_window;
        
        m_current_fps = 1000.0f / frame_time_ms;
        m_stats.total_frames++;
        
        UpdateStatistics();
    }

    float UIFrameRateMonitor::GetFrameRateStability() const
    {
        std::lock_guard<std::mutex> lock(m_mutex);

        if (m_average_fps <= 0.0f)
            return 0.0f;

        // 稳定性 = 1 - (方差 / 平均值的平方)
        float normalized_variance = m_frame_time_variance / (m_average_fps * m_average_fps);
        return std::max(0.0f, 1.0f - normalized_variance);
    }

    bool UIFrameRateMonitor::IsFrameRateDropDetected() const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        // 简单的帧率下降检测：当前帧率比平均帧率低20%以上
        return m_current_fps < m_average_fps * 0.8f;
    }

    void UIFrameRateMonitor::UpdateStatistics()
    {
        // 计算平均帧时间
        float sum = 0.0f;
        uint32_t valid_samples = 0;
        
        for (float frame_time : m_frame_times)
        {
            if (frame_time > 0.0f)
            {
                sum += frame_time;
                valid_samples++;
            }
        }
        
        if (valid_samples > 0)
        {
            float average_frame_time = sum / valid_samples;
            m_average_fps = 1000.0f / average_frame_time;
            m_frame_time_variance = CalculateVariance();
            
            m_stats.current_fps = m_current_fps;
            m_stats.average_fps = m_average_fps;
            m_stats.min_fps = std::min(m_stats.min_fps, m_current_fps);
            m_stats.max_fps = std::max(m_stats.max_fps, m_current_fps);
            m_stats.stability = GetFrameRateStability();
            m_stats.frame_time_variance = m_frame_time_variance;
        }
    }

    float UIFrameRateMonitor::CalculateVariance() const
    {
        if (m_frame_times.empty())
            return 0.0f;
            
        float sum = 0.0f;
        uint32_t valid_samples = 0;
        
        for (float frame_time : m_frame_times)
        {
            if (frame_time > 0.0f)
            {
                sum += frame_time;
                valid_samples++;
            }
        }
        
        if (valid_samples <= 1)
            return 0.0f;
            
        float mean = sum / valid_samples;
        float variance_sum = 0.0f;
        
        for (float frame_time : m_frame_times)
        {
            if (frame_time > 0.0f)
            {
                float diff = frame_time - mean;
                variance_sum += diff * diff;
            }
        }
        
        return variance_sum / valid_samples;
    }
}
