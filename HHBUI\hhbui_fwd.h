/*
** 版权所有 （c） 2025 HHBUI ：https://hhbui.com/
** 保留所有权利.
** HHBUI 前向声明头文件 - 减少编译依赖
*/

#pragma once

namespace HHBUI
{
    // 基础类前向声明
    class UIBase;
    class UIEngine;
    
    // 窗口和控件
    class UIWnd;
    class UIControl;
    class UILayout;
    class UICanvas;
    
    // 元素类
    class UIFont;
    class UIBrush;
    class UIPath;
    class UIImage;
    class UIRegion;
    class UIColor;
    
    // 渲染相关
    class UIRenderThread;
    class UIFPSCounter;
    
    // 控件类
    class UIButton;
    class UIEdit;
    class UIStatic;
    class UIList;
    class UIComboBox;
    class UITreeView;
    class UIProgress;
    class UISlider;
    
    // 工具类
    class UIZip;
    class UIAnimation;
    class UIScroll;
    class UIMenu;
    
    // 类型别名
    using UIimage = LPVOID;
    using UIbrush = LPVOID;
    using UIzip = LPVOID;
    using WndView = INT;
    
    // 回调函数类型
    using ClsPROC = LRESULT(CALLBACK*)(HWND, INT, WPARAM, LPARAM);
    using MsgPROC = LRESULT(CALLBACK*)(HWND, LPVOID, LPVOID, INT, INT, WPARAM, LPARAM);
    using EventHandlerPROC = LRESULT(CALLBACK*)(LPVOID, LPVOID, INT, INT, WPARAM, LPARAM);
    using AnimationPROC = void(CALLBACK*)(LPVOID, INT, BOOL, BOOL, DOUBLE, DOUBLE, DOUBLE, LONG_PTR, LONG_PTR, LONG_PTR, LONG_PTR);
}
