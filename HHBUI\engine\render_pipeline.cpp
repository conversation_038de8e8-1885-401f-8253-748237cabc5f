/**
** =====================================================================================
**
**       文件名称: render_pipeline.cpp
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】可配置渲染管线系统 - 现代化渲染管线架构框架 （实现文件）
**
** =====================================================================================
**/

#include "pch.h"
#include "render_pipeline.h"
#include "common/Exception.h"

namespace HHBUI
{
    // 全局渲染管线管理器实例
    UIRenderPipelineManager* g_render_pipeline_manager = nullptr;

    // UIGBufferManager 实现
    UIGBufferManager::UIGBufferManager()
        : m_width(0)
        , m_height(0)
    {
        memset(m_rtvs, 0, sizeof(m_rtvs));
    }

    UIGBufferManager::~UIGBufferManager()
    {
        Shutdown();
    }

    HRESULT UIGBufferManager::Initialize(ID3D11Device* device, uint32_t width, uint32_t height)
    {
        try
        {
            if (!device)
                return E_INVALIDARG;

            m_width = width;
            m_height = height;

            throw_if_failed(CreateGBufferTextures(device), L"创建G-Buffer纹理失败");
            throw_if_failed(CreateGBufferViews(device), L"创建G-Buffer视图失败");

            return S_OK;
        }
        catch (const Exception& ex)
        {
            return ex.GetStatus();
        }
    }

    void UIGBufferManager::Shutdown()
    {
        m_albedo_texture.Reset();
        m_normal_texture.Reset();
        m_material_texture.Reset();
        m_depth_texture.Reset();
        
        m_albedo_rtv.Reset();
        m_normal_rtv.Reset();
        m_material_rtv.Reset();
        m_depth_dsv.Reset();
        
        m_albedo_srv.Reset();
        m_normal_srv.Reset();
        m_material_srv.Reset();
        m_depth_srv.Reset();

        memset(m_rtvs, 0, sizeof(m_rtvs));
    }

    HRESULT UIGBufferManager::ResizeGBuffer(uint32_t width, uint32_t height)
    {
        if (m_width == width && m_height == height)
            return S_OK;

        Shutdown();
        return Initialize(nullptr, width, height);
    }

    HRESULT UIGBufferManager::BeginGBufferFill(ID3D11DeviceContext* context)
    {
        if (!context)
            return E_INVALIDARG;

        // 清空G-Buffer
        ClearGBuffer(context);

        // 设置G-Buffer渲染目标
        context->OMSetRenderTargets(3, m_rtvs, m_depth_dsv.Get());

        return S_OK;
    }

    HRESULT UIGBufferManager::EndGBufferFill(ID3D11DeviceContext* context)
    {
        // 这里可以添加任何清理逻辑
        return S_OK;
    }

    HRESULT UIGBufferManager::BindGBufferForReading(ID3D11DeviceContext* context, uint32_t start_slot)
    {
        if (!context)
            return E_INVALIDARG;

        ID3D11ShaderResourceView* srvs[] = {
            m_albedo_srv.Get(),
            m_normal_srv.Get(),
            m_material_srv.Get(),
            m_depth_srv.Get()
        };

        context->PSSetShaderResources(start_slot, 4, srvs);
        return S_OK;
    }

    void UIGBufferManager::ClearGBuffer(ID3D11DeviceContext* context)
    {
        if (!context)
            return;

        float clear_color[4] = { 0.0f, 0.0f, 0.0f, 0.0f };
        
        context->ClearRenderTargetView(m_albedo_rtv.Get(), clear_color);
        context->ClearRenderTargetView(m_normal_rtv.Get(), clear_color);
        context->ClearRenderTargetView(m_material_rtv.Get(), clear_color);
        context->ClearDepthStencilView(m_depth_dsv.Get(), D3D11_CLEAR_DEPTH | D3D11_CLEAR_STENCIL, 1.0f, 0);
    }

    HRESULT UIGBufferManager::CreateGBufferTextures(ID3D11Device* device)
    {
        try
        {
            // 创建Albedo纹理 (RGBA8)
            D3D11_TEXTURE2D_DESC desc = {};
            desc.Width = m_width;
            desc.Height = m_height;
            desc.MipLevels = 1;
            desc.ArraySize = 1;
            desc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
            desc.SampleDesc.Count = 1;
            desc.Usage = D3D11_USAGE_DEFAULT;
            desc.BindFlags = D3D11_BIND_RENDER_TARGET | D3D11_BIND_SHADER_RESOURCE;

            throw_if_failed(
                device->CreateTexture2D(&desc, nullptr, &m_albedo_texture),
                L"创建Albedo纹理失败"
            );

            // 创建Normal纹理 (RGBA8)
            throw_if_failed(
                device->CreateTexture2D(&desc, nullptr, &m_normal_texture),
                L"创建Normal纹理失败"
            );

            // 创建Material纹理 (RGBA8)
            throw_if_failed(
                device->CreateTexture2D(&desc, nullptr, &m_material_texture),
                L"创建Material纹理失败"
            );

            // 创建深度纹理
            desc.Format = DXGI_FORMAT_D24_UNORM_S8_UINT;
            desc.BindFlags = D3D11_BIND_DEPTH_STENCIL | D3D11_BIND_SHADER_RESOURCE;

            throw_if_failed(
                device->CreateTexture2D(&desc, nullptr, &m_depth_texture),
                L"创建深度纹理失败"
            );

            return S_OK;
        }
        catch (const Exception& ex)
        {
            return ex.GetStatus();
        }
    }

    HRESULT UIGBufferManager::CreateGBufferViews(ID3D11Device* device)
    {
        try
        {
            // 创建渲染目标视图
            D3D11_RENDER_TARGET_VIEW_DESC rtv_desc = {};
            rtv_desc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
            rtv_desc.ViewDimension = D3D11_RTV_DIMENSION_TEXTURE2D;

            throw_if_failed(
                device->CreateRenderTargetView(m_albedo_texture.Get(), &rtv_desc, &m_albedo_rtv),
                L"创建Albedo RTV失败"
            );

            throw_if_failed(
                device->CreateRenderTargetView(m_normal_texture.Get(), &rtv_desc, &m_normal_rtv),
                L"创建Normal RTV失败"
            );

            throw_if_failed(
                device->CreateRenderTargetView(m_material_texture.Get(), &rtv_desc, &m_material_rtv),
                L"创建Material RTV失败"
            );

            // 创建深度模板视图
            D3D11_DEPTH_STENCIL_VIEW_DESC dsv_desc = {};
            dsv_desc.Format = DXGI_FORMAT_D24_UNORM_S8_UINT;
            dsv_desc.ViewDimension = D3D11_DSV_DIMENSION_TEXTURE2D;

            throw_if_failed(
                device->CreateDepthStencilView(m_depth_texture.Get(), &dsv_desc, &m_depth_dsv),
                L"创建深度模板视图失败"
            );

            // 创建着色器资源视图
            D3D11_SHADER_RESOURCE_VIEW_DESC srv_desc = {};
            srv_desc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
            srv_desc.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE2D;
            srv_desc.Texture2D.MipLevels = 1;

            throw_if_failed(
                device->CreateShaderResourceView(m_albedo_texture.Get(), &srv_desc, &m_albedo_srv),
                L"创建Albedo SRV失败"
            );

            throw_if_failed(
                device->CreateShaderResourceView(m_normal_texture.Get(), &srv_desc, &m_normal_srv),
                L"创建Normal SRV失败"
            );

            throw_if_failed(
                device->CreateShaderResourceView(m_material_texture.Get(), &srv_desc, &m_material_srv),
                L"创建Material SRV失败"
            );

            // 创建深度SRV
            srv_desc.Format = DXGI_FORMAT_R24_UNORM_X8_TYPELESS;
            throw_if_failed(
                device->CreateShaderResourceView(m_depth_texture.Get(), &srv_desc, &m_depth_srv),
                L"创建深度SRV失败"
            );

            // 设置RTV数组
            m_rtvs[0] = m_albedo_rtv.Get();
            m_rtvs[1] = m_normal_rtv.Get();
            m_rtvs[2] = m_material_rtv.Get();

            return S_OK;
        }
        catch (const Exception& ex)
        {
            return ex.GetStatus();
        }
    }

    // UILightingManager 实现
    UILightingManager::UILightingManager()
        : m_width(0)
        , m_height(0)
        , m_constants{}
    {
    }

    UILightingManager::~UILightingManager()
    {
        Shutdown();
    }

    HRESULT UILightingManager::Initialize(ID3D11Device* device, uint32_t width, uint32_t height)
    {
        try
        {
            if (!device)
                return E_INVALIDARG;

            m_width = width;
            m_height = height;

            throw_if_failed(CreateLightingShaders(device), L"创建光照着色器失败");
            throw_if_failed(CreateLightingBuffers(device), L"创建光照缓冲区失败");

            // 设置默认环境光
            m_constants.ambient_color = DirectX::XMFLOAT4(0.1f, 0.1f, 0.1f, 1.0f);

            return S_OK;
        }
        catch (const Exception& ex)
        {
            return ex.GetStatus();
        }
    }

    void UILightingManager::Shutdown()
    {
        m_directional_lights.clear();
        m_point_lights.clear();
        m_spot_lights.clear();

        m_lighting_constants_buffer.Reset();
        m_directional_lights_buffer.Reset();
        m_point_lights_buffer.Reset();
        m_spot_lights_buffer.Reset();

        m_deferred_lighting_vs.Reset();
        m_deferred_lighting_ps.Reset();
        m_sampler_state.Reset();
    }

    HRESULT UILightingManager::ExecuteDeferredLighting(ID3D11DeviceContext* context,
                                                      UIGBufferManager* gbuffer_manager,
                                                      ID3D11RenderTargetView* output_rtv)
    {
        if (!context || !gbuffer_manager || !output_rtv)
            return E_INVALIDARG;

        try
        {
            // 更新光照数据
            throw_if_failed(UpdateLightingData(context), L"更新光照数据失败");

            // 设置着色器
            context->VSSetShader(m_deferred_lighting_vs.Get(), nullptr, 0);
            context->PSSetShader(m_deferred_lighting_ps.Get(), nullptr, 0);

            // 绑定G-Buffer
            throw_if_failed(gbuffer_manager->BindGBufferForReading(context, 0), L"绑定G-Buffer失败");

            // 设置常量缓冲区
            context->PSSetConstantBuffers(0, 1, m_lighting_constants_buffer.GetAddressOf());
            context->PSSetConstantBuffers(1, 1, m_directional_lights_buffer.GetAddressOf());
            context->PSSetConstantBuffers(2, 1, m_point_lights_buffer.GetAddressOf());
            context->PSSetConstantBuffers(3, 1, m_spot_lights_buffer.GetAddressOf());

            // 设置采样器
            context->PSSetSamplers(0, 1, m_sampler_state.GetAddressOf());

            // 设置渲染目标
            context->OMSetRenderTargets(1, &output_rtv, nullptr);

            // 绘制全屏四边形
            context->IASetPrimitiveTopology(D3D11_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP);
            context->Draw(4, 0);

            return S_OK;
        }
        catch (const Exception& ex)
        {
            return ex.GetStatus();
        }
    }

    HRESULT UILightingManager::ExecuteForwardLighting(ID3D11DeviceContext* context,
                                                     const std::vector<RenderBatch>& batches)
    {
        if (!context)
            return E_INVALIDARG;

        try
        {
            // 更新光照数据
            throw_if_failed(UpdateLightingData(context), L"更新光照数据失败");

            // 对于前向渲染，光照数据会在渲染每个批次时使用
            // 这里只是确保光照数据是最新的

            return S_OK;
        }
        catch (const Exception& ex)
        {
            return ex.GetStatus();
        }
    }

    void UILightingManager::AddDirectionalLight(const DirectX::XMFLOAT3& direction,
                                               const DirectX::XMFLOAT3& color,
                                               float intensity)
    {
        DirectionalLight light;
        light.direction = direction;
        light.color = color;
        light.intensity = intensity;
        light.padding = 0.0f;

        m_directional_lights.push_back(light);
        m_constants.directional_light_count = static_cast<uint32_t>(m_directional_lights.size());
    }

    void UILightingManager::AddPointLight(const DirectX::XMFLOAT3& position,
                                         const DirectX::XMFLOAT3& color,
                                         float intensity,
                                         float range)
    {
        PointLight light;
        light.position = position;
        light.intensity = intensity;
        light.color = color;
        light.range = range;

        m_point_lights.push_back(light);
        m_constants.point_light_count = static_cast<uint32_t>(m_point_lights.size());
    }

    void UILightingManager::AddSpotLight(const DirectX::XMFLOAT3& position,
                                        const DirectX::XMFLOAT3& direction,
                                        const DirectX::XMFLOAT3& color,
                                        float intensity,
                                        float range,
                                        float inner_cone,
                                        float outer_cone)
    {
        SpotLight light;
        light.position = position;
        light.intensity = intensity;
        light.direction = direction;
        light.range = range;
        light.color = color;
        light.inner_cone = inner_cone;
        light.outer_cone = outer_cone;
        light.padding = DirectX::XMFLOAT3(0.0f, 0.0f, 0.0f);

        m_spot_lights.push_back(light);
        m_constants.spot_light_count = static_cast<uint32_t>(m_spot_lights.size());
    }

    void UILightingManager::ClearLights()
    {
        m_directional_lights.clear();
        m_point_lights.clear();
        m_spot_lights.clear();

        m_constants.directional_light_count = 0;
        m_constants.point_light_count = 0;
        m_constants.spot_light_count = 0;
    }

    HRESULT UILightingManager::UpdateLightingData(ID3D11DeviceContext* context)
    {
        if (!context)
            return E_INVALIDARG;

        try
        {
            // 更新光照常量缓冲区
            D3D11_MAPPED_SUBRESOURCE mapped;
            throw_if_failed(
                context->Map(m_lighting_constants_buffer.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0, &mapped),
                L"映射光照常量缓冲区失败"
            );

            memcpy(mapped.pData, &m_constants, sizeof(m_constants));
            context->Unmap(m_lighting_constants_buffer.Get(), 0);

            // 更新方向光缓冲区
            if (!m_directional_lights.empty())
            {
                throw_if_failed(
                    context->Map(m_directional_lights_buffer.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0, &mapped),
                    L"映射方向光缓冲区失败"
                );

                memcpy(mapped.pData, m_directional_lights.data(), 
                       m_directional_lights.size() * sizeof(DirectionalLight));
                context->Unmap(m_directional_lights_buffer.Get(), 0);
            }

            // 更新点光源缓冲区
            if (!m_point_lights.empty())
            {
                throw_if_failed(
                    context->Map(m_point_lights_buffer.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0, &mapped),
                    L"映射点光源缓冲区失败"
                );

                memcpy(mapped.pData, m_point_lights.data(), 
                       m_point_lights.size() * sizeof(PointLight));
                context->Unmap(m_point_lights_buffer.Get(), 0);
            }

            // 更新聚光灯缓冲区
            if (!m_spot_lights.empty())
            {
                throw_if_failed(
                    context->Map(m_spot_lights_buffer.Get(), 0, D3D11_MAP_WRITE_DISCARD, 0, &mapped),
                    L"映射聚光灯缓冲区失败"
                );

                memcpy(mapped.pData, m_spot_lights.data(), 
                       m_spot_lights.size() * sizeof(SpotLight));
                context->Unmap(m_spot_lights_buffer.Get(), 0);
            }

            return S_OK;
        }
        catch (const Exception& ex)
        {
            return ex.GetStatus();
        }
    }

    HRESULT UILightingManager::CreateLightingShaders(ID3D11Device* device)
    {
        // 这里应该编译实际的着色器
        // 为了简化，我们跳过着色器编译
        return S_OK;
    }

    HRESULT UILightingManager::CreateLightingBuffers(ID3D11Device* device)
    {
        try
        {
            // 创建光照常量缓冲区
            D3D11_BUFFER_DESC buffer_desc = {};
            buffer_desc.ByteWidth = sizeof(LightingConstants);
            buffer_desc.Usage = D3D11_USAGE_DYNAMIC;
            buffer_desc.BindFlags = D3D11_BIND_CONSTANT_BUFFER;
            buffer_desc.CPUAccessFlags = D3D11_CPU_ACCESS_WRITE;

            throw_if_failed(
                device->CreateBuffer(&buffer_desc, nullptr, &m_lighting_constants_buffer),
                L"创建光照常量缓冲区失败"
            );

            // 创建方向光缓冲区
            buffer_desc.ByteWidth = sizeof(DirectionalLight) * 8; // 最多8个方向光
            throw_if_failed(
                device->CreateBuffer(&buffer_desc, nullptr, &m_directional_lights_buffer),
                L"创建方向光缓冲区失败"
            );

            // 创建点光源缓冲区
            buffer_desc.ByteWidth = sizeof(PointLight) * 64; // 最多64个点光源
            throw_if_failed(
                device->CreateBuffer(&buffer_desc, nullptr, &m_point_lights_buffer),
                L"创建点光源缓冲区失败"
            );

            // 创建聚光灯缓冲区
            buffer_desc.ByteWidth = sizeof(SpotLight) * 32; // 最多32个聚光灯
            throw_if_failed(
                device->CreateBuffer(&buffer_desc, nullptr, &m_spot_lights_buffer),
                L"创建聚光灯缓冲区失败"
            );

            // 创建采样器状态
            D3D11_SAMPLER_DESC sampler_desc = {};
            sampler_desc.Filter = D3D11_FILTER_MIN_MAG_MIP_LINEAR;
            sampler_desc.AddressU = D3D11_TEXTURE_ADDRESS_CLAMP;
            sampler_desc.AddressV = D3D11_TEXTURE_ADDRESS_CLAMP;
            sampler_desc.AddressW = D3D11_TEXTURE_ADDRESS_CLAMP;
            sampler_desc.ComparisonFunc = D3D11_COMPARISON_NEVER;
            sampler_desc.MinLOD = 0;
            sampler_desc.MaxLOD = D3D11_FLOAT32_MAX;

            throw_if_failed(
                device->CreateSamplerState(&sampler_desc, &m_sampler_state),
                L"创建采样器状态失败"
            );

            return S_OK;
        }
        catch (const Exception& ex)
        {
            return ex.GetStatus();
        }
    }
}
