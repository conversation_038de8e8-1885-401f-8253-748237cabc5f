﻿/**
 * @file coordinate.h
 * @brief 坐标相关类型定义
 */
#pragma once

// 防止Windows宏干扰
#ifndef NOMINMAX
#define NOMINMAX
#endif

#include <algorithm>
#include <cmath>

// 确保使用函数版本而不是宏版本
#ifdef min
#undef min
#endif
#ifdef max
#undef max
#endif
namespace HHBUI
{
	struct ExPoint;
	struct ExPointF;

	/// 整数坐标
	struct ExPoint : public POINT
	{
		ExPoint() { x = y = 0; }
		ExPoint(long X, long Y) { x = X; y = Y; }
		ExPoint(POINT* pt) { x = pt->x; y = pt->y; }
		ExPoint(POINT& pt) { x = pt.x; y = pt.y; }
		ExPoint(SIZE* sz) { x = sz->cx; y = sz->cy; }
		ExPoint(SIZE& sz) { x = sz.cx; y = sz.cy; }
		ExPoint(LPARAM lParam) { x = GET_X_LPARAM(lParam); y = GET_Y_LPARAM(lParam); }

		inline void Offset(long x, long y) { this->x += x; this->y += y; }

		inline bool operator==(ExPoint& pt) const
		{
			return x == pt.x && y == pt.y;
		}

		ExPointF ToPointF() const;
	};

	/// 小数坐标
	struct ExPointF
	{
		float x;
		float y;

		ExPointF() { x = y = 0; }
		ExPointF(float X, float Y) { x = X; y = Y; }
		ExPointF(POINT* pt) { x = pt->x; y = pt->y; }
		ExPointF(POINT& pt) { x = pt.x; y = pt.y; }

		inline void Offset(float x, float y) { this->x += x; this->y += y; }

		inline bool operator==(ExPointF& pt) const
		{
			return float_eq(x, pt.x) && float_eq(y, pt.y);
		}

		inline ExPointF Rounding()
		{
			return ExPointF(roundf_(x), roundf_(y));
		}

		ExPoint ToPoint() const;
	};

	inline ExPointF ExPoint::ToPointF() const { return ExPointF(x, y); }
	inline ExPoint ExPointF::ToPoint() const { return ExPoint(roundf_(x), roundf_(y)); }

	/////////////////////////////////

	struct ExRect;
	struct ExRectF;

	/// 整数矩形
	struct ExRect : public RECT
	{
		ExRect() { left = top = right = bottom = 0; }
		ExRect(long l, long t, long r, long b) { left = l; top = t; right = r; bottom = b; }
		ExRect(long l, long t, long w, long h, bool unuse) { left = l; top = t; right = l + w; bottom = t + h; }
		ExRect(RECT* rc) { left = rc->left; top = rc->top; right = rc->right; bottom = rc->bottom; }
		ExRect(RECT& rc) { left = rc.left; top = rc.top; right = rc.right; bottom = rc.bottom; }

		inline long Width()  const { return right - left; }
		inline long Height() const { return bottom - top; }

		inline long GetLeft() const { return (std::min)(left, right); }
		inline long GetTop()  const { return (std::min)(top, bottom); }
		inline long GetRight() const { return (std::max)(left, right); }
		inline long GetBottom() const { return (std::max)(top, bottom); }
		inline long GetWidth() const { return std::abs(right - left); }
		inline long GetHeight() const { return std::abs(bottom - top); }
		inline long GetHorzCenter() const { return (left + right) / 2; }
		inline long GetVertCenter() const { return (top + bottom) / 2; }
		inline ExRect& OffsetSize(long size) { left -= size; top -= size; right -= size; bottom -= size; return *this; }
		inline ExRect& Offset(long x, long y) { left += x; top += y; right += x; bottom += y; return *this; }
		inline ExRect& Inflate(long x, long y) { left -= x; top -= y; right += x; bottom += y; return *this; }
		inline bool empty() const { return ::IsRectEmpty(this); }
		inline bool PtInRect(long x, long y) const { return ::PtInRect(this, POINT{ x,y }); }

		inline ExRect Normalize() const
		{
			ExRect rc;
			rc.left = (std::min)(left, right);
			rc.top = (std::min)(top, bottom);
			rc.right = (std::max)(left, right);
			rc.bottom = (std::max)(top, bottom);
			return rc;
		}
		void UnionRect(const ExRect& rect1, const ExRect& rect2) {
			// 计算左上角的最小值和右下角的最大值
			this->left = (std::min)(rect1.left, rect2.left);
			this->top = (std::min)(rect1.top, rect2.top);
			this->right = (std::max)(rect1.right, rect2.right);
			this->bottom = (std::max)(rect1.bottom, rect2.bottom);
		}
		bool IntersectRect(const ExRect& rect1, const ExRect& rect2) {
			// 计算交集矩形的坐标
			this->left = (std::max)(rect1.left, rect2.left);
			this->top = (std::max)(rect1.top, rect2.top);
			this->right = (std::min)(rect1.right, rect2.right);
			this->bottom = (std::min)(rect1.bottom, rect2.bottom);

			// 检查交集矩形是否有效（即是否相交）
			if (this->left < this->right && this->top < this->bottom) {
				return true;  // 矩形相交
			}
			else {
				// 如果不相交，恢复为空矩形
				this->left = this->top = this->right = this->bottom = 0;
				return false;  // 矩形不相交
			}
		}
		inline ExRect CenterRect(float width, float height)
		{
			return ExRect(
				(left + right - width) / 2,
				(top + bottom - height) / 2,
				(left + right + width) / 2,
				(top + bottom + height) / 2
			);
		}


		inline bool operator==(ExRect& rc) const
		{
			return left == rc.left && top == rc.top && right == rc.right && bottom == rc.bottom;
		}

		ExRectF ToRectF() const;
	};

	/// 小数矩形
	struct ExRectF
	{
		float left;
		float top;
		float right;
		float bottom;

		ExRectF() { left = top = right = bottom = 0.0f; }
		ExRectF(float l, float t, float r, float b) { left = l; top = t; right = r; bottom = b; }
		ExRectF(float l, float t, float w, float h, bool unuse) { left = l; top = t; right = l + w; bottom = t + h; }
		ExRectF(RECT* rc) { left = rc->left; top = rc->top; right = rc->right; bottom = rc->bottom; }
		ExRectF(RECT& rc) { left = rc.left; top = rc.top; right = rc.right; bottom = rc.bottom; }

		inline float Width() const { return right - left; }
		inline float Height() const { return bottom - top; }

		// 为了兼容性添加width和height属性
		inline float width() const { return right - left; }
		inline float height() const { return bottom - top; }
		inline void width(float w) { right = left + w; }
		inline void height(float h) { bottom = top + h; }

		inline float GetLeft() const { return (std::min)(left, right); }
		inline float GetTop() const { return (std::min)(top, bottom); }
		inline float GetRight() const { return (std::max)(left, right); }
		inline float GetBottom() const { return (std::max)(top, bottom); }
		inline float GetWidth() const { return std::fabs(right - left); }
		inline float GetHeight() const { return std::fabs(bottom - top); }
		inline float GetHorzCenter() const { return (left + right) / 2; }
		inline float GetVertCenter() const { return (top + bottom) / 2; }
		inline ExRectF& OffsetSize(long size) { left -= size; top -= size; right -= size; bottom -= size; return *this; }
		inline ExRectF& Offset(float x, float y) { left += x; top += y; right += x; bottom += y; return *this; }
		inline ExRectF& Inflate(float x, float y) { left -= x; top -= y; right += x; bottom += y; return *this; }
		inline bool empty() const { return (left == 0.0f && top == 0.0f && right == 0.0f && bottom == 0.0f); }
		inline bool PtInRect(float x, float y) const { return x >= left && x <= right && y >= top && y <= bottom; }
		inline ExRectF Normalize() const
		{
			ExRectF rc;
			rc.left = (std::min)(left, right);
			rc.top = (std::min)(top, bottom);
			rc.right = (std::max)(left, right);
			rc.bottom = (std::max)(top, bottom);
			return rc;
		}
		inline ExRectF fScale(float fDpi) const
		{
			ExRectF rc;
			rc.left = left * fDpi;
			rc.top = top * fDpi;
			rc.right = right * fDpi;
			rc.bottom = bottom * fDpi;
			return rc;
		}
		inline ExRectF fScaleNo(float fDpi) const
		{
			ExRectF rc;
			rc.left = left / fDpi;
			rc.top = top / fDpi;
			rc.right = right / fDpi;
			rc.bottom = bottom / fDpi;
			return rc;
		}
		void UnionRect(const ExRectF& rect1, const ExRectF& rect2) {
			// 计算左上角的最小值和右下角的最大值
			this->left = (std::min)(rect1.left, rect2.left);
			this->top = (std::min)(rect1.top, rect2.top);
			this->right = (std::max)(rect1.right, rect2.right);
			this->bottom = (std::max)(rect1.bottom, rect2.bottom);
		}
		bool IntersectRect(const ExRectF& rect1, const ExRectF& rect2) {
			// 计算交集矩形的坐标
			this->left = (std::max)(rect1.left, rect2.left);
			this->top = (std::max)(rect1.top, rect2.top);
			this->right = (std::min)(rect1.right, rect2.right);
			this->bottom = (std::min)(rect1.bottom, rect2.bottom);

			// 检查交集矩形是否有效（即是否相交）
			if (this->left < this->right && this->top < this->bottom) {
				return true;  // 矩形相交
			}
			else {
				// 如果不相交，恢复为空矩形
				this->left = this->top = this->right = this->bottom = 0;
				return false;  // 矩形不相交
			}
		}
		inline ExRectF Rounding()
		{
			return ExRectF(roundf_(left), roundf_(top), roundf_(right), roundf_(bottom));
		}
		inline void MoveToX(int x) 
		{
			int width = right - left;
			left = x;
			right = left + width;
		}
		inline void MoveToY(int y) 
		{
			int height = bottom - top;
			top = y;
			bottom = top + height;
		}
		inline ExRectF CenterRect(float width, float height)
		{
			return ExRectF(
				(left + right - width) / 2,
				(top + bottom - height) / 2,
				(left + right + width) / 2,
				(top + bottom + height) / 2
			);
		}

		inline bool operator==(ExRectF& rc) const
		{
			return float_eq(left, rc.left) && float_eq(top, rc.top) &&
				float_eq(right, rc.right) && float_eq(bottom, rc.bottom);
		}

		ExRect ToRect() const;
	};

	inline ExRectF ExRect::ToRectF() const { return ExRectF(left, top, right, bottom); }
	inline ExRect ExRectF::ToRect() const { return ExRect(roundf_(left), roundf_(top), roundf_(right), roundf_(bottom)); }

	struct ExNumU
	{
		float value;
		uint8_t unit;
	};

	struct ExPointU : public ExPointF
	{
		uint8_t units[4];
	};

	struct ExRectU : public ExRectF
	{
		uint8_t units[4];
	};
	inline void _offset_(bool reverse, float& last)
	{
		if (reverse) { last -= 0.5F; }
		else { last += 0.5F; }
	}
	inline void _offset_(bool reverse, ExPointF& last)
	{
		_offset_(reverse, last.x);
		_offset_(reverse, last.y);
	}
	inline void _offset_(bool reverse, ExRectF& last)
	{
		_offset_(reverse, last.left);
		_offset_(reverse, last.top);
		_offset_(reverse, last.right);
		_offset_(reverse, last.bottom);
	}
	inline void _offset_(bool reverse, D2D1_POINT_2F& last)
	{
		_offset_(reverse, last.x);
		_offset_(reverse, last.y);
	}
	inline void _offset_(bool reverse, D2D1_RECT_F& last)
	{
		_offset_(reverse, last.left);
		_offset_(reverse, last.top);
		_offset_(reverse, last.right);
		_offset_(reverse, last.bottom);
	}

	template<typename T, class ...Args>
	inline void _offset_(bool reverse, T& first, Args&... args)
	{
		_offset_(reverse, first);
		_offset_(reverse, args...);
	}
}

#define _expand_point_(point)		point.x, point.y
#define _expand_rect_(rect)			rect.left, rect.top, rect.right, rect.bottom

#define _expand_point_ptr_(point)	point->x, point->y
#define _expand_rect_ptr_(rect)		rect->left, rect->top, rect->right, rect->bottom

#define _expand_point_split_(point, _split_)		point.x _split_ point.y
#define _expand_rect_split_(rect, _split_)			rect.left _split_ rect.top _split_ rect.right _split_ rect.bottom

#define _expand_point_ptr_split_(point, _split_)	point->x _split_ point->y
#define _expand_rect_ptr_split_(rect, _split_)		rect->left _split_ rect->top _split_ rect->right _split_ rect->bottom




