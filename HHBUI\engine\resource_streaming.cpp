/**
** =====================================================================================
**
**       文件名称: resource_streaming.cpp
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】资源流式管理系统 - 智能资源预加载与LOD管理框架 （实现文件）
**
** =====================================================================================
**/

#include "pch.h"
#include "resource_streaming.h"
#include "common/Exception.h"

namespace HHBUI
{
    // 全局资源流式管理器实例
    UIResourceStreamingManager* g_resource_streaming_manager = nullptr;

    // UILODManager 实现
    UILODManager::UILODManager()
        : m_auto_adjustment_enabled(false)
        , m_target_frametime_ms(16.67f)
        , m_frametime_index(0)
        , m_stats{}
    {
        m_frametime_history.resize(60, 16.67f); // 60帧历史
    }

    UILODManager::~UILODManager()
    {
        Shutdown();
    }

    HRESULT UILODManager::Initialize()
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        // 设置默认距离阈值
        m_distance_thresholds[ResourceType::TEXTURE] = {10.0f, 25.0f, 50.0f, 100.0f, 200.0f};
        m_distance_thresholds[ResourceType::MESH] = {5.0f, 15.0f, 30.0f, 60.0f, 120.0f};
        
        m_stats = LODStats{};
        return S_OK;
    }

    void UILODManager::Shutdown()
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_distance_thresholds.clear();
        m_lod_resources.clear();
        m_frametime_history.clear();
        m_stats = LODStats{};
    }

    void UILODManager::RegisterLODResource(const std::wstring& base_path,
                                          const std::vector<std::wstring>& lod_paths)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_lod_resources[base_path] = lod_paths;
    }

    LODLevel UILODManager::GetAppropriateLevel(float distance, float screen_size,
                                              ResourceType type) const
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        auto it = m_distance_thresholds.find(type);
        if (it == m_distance_thresholds.end())
            return LODLevel::LOD_0;

        const auto& thresholds = it->second;
        
        // 根据距离选择LOD级别
        for (size_t i = 0; i < thresholds.size(); ++i)
        {
            if (distance <= thresholds[i])
            {
                return static_cast<LODLevel>(i);
            }
        }
        
        return LODLevel::LOD_4; // 最低质量
    }

    void UILODManager::SetLODDistanceThresholds(ResourceType type, 
                                               const std::vector<float>& thresholds)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_distance_thresholds[type] = thresholds;
    }

    void UILODManager::SetAutoLODAdjustment(bool enable, float target_frametime_ms)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_auto_adjustment_enabled = enable;
        m_target_frametime_ms = target_frametime_ms;
    }

    void UILODManager::UpdateLOD(float current_frametime_ms)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        // 更新帧时间历史
        m_frametime_history[m_frametime_index] = current_frametime_ms;
        m_frametime_index = (m_frametime_index + 1) % m_frametime_history.size();
        
        if (m_auto_adjustment_enabled)
        {
            AdjustLODLevels(current_frametime_ms);
        }
    }

    void UILODManager::AdjustLODLevels(float current_frametime_ms)
    {
        // 计算平均帧时间
        float avg_frametime = 0.0f;
        for (float ft : m_frametime_history)
        {
            avg_frametime += ft;
        }
        avg_frametime /= m_frametime_history.size();
        
        // 如果帧时间超过目标，降低LOD质量
        if (avg_frametime > m_target_frametime_ms * 1.2f)
        {
            // 降低LOD质量的逻辑
            for (auto& [type, thresholds] : m_distance_thresholds)
            {
                for (float& threshold : thresholds)
                {
                    threshold *= 0.9f; // 减少10%的距离阈值
                }
            }
        }
        // 如果帧时间远低于目标，提高LOD质量
        else if (avg_frametime < m_target_frametime_ms * 0.8f)
        {
            // 提高LOD质量的逻辑
            for (auto& [type, thresholds] : m_distance_thresholds)
            {
                for (float& threshold : thresholds)
                {
                    threshold *= 1.1f; // 增加10%的距离阈值
                }
            }
        }
    }

    float UILODManager::CalculateScreenSize(float distance, float object_size) const
    {
        // 简化的屏幕大小计算
        return object_size / distance;
    }

    // UITextureAtlasManager 实现
    UITextureAtlasManager::UITextureAtlasManager()
        : m_device(nullptr)
        , m_stats{}
    {
    }

    UITextureAtlasManager::~UITextureAtlasManager()
    {
        Shutdown();
    }

    HRESULT UITextureAtlasManager::Initialize(ID3D11Device* device)
    {
        if (!device)
            return E_INVALIDARG;

        m_device = device;
        m_stats = AtlasStats{};
        return S_OK;
    }

    void UITextureAtlasManager::Shutdown()
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_atlases.clear();
        m_device = nullptr;
        m_stats = AtlasStats{};
    }

    HRESULT UITextureAtlasManager::CreateAtlas(const std::vector<std::wstring>& texture_paths,
                                              uint32_t atlas_width, uint32_t atlas_height,
                                              const std::wstring& atlas_name)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        try
        {
            // 加载所有纹理并获取尺寸
            std::vector<TextureRect> textures;
            for (const auto& path : texture_paths)
            {
                TextureRect rect;
                rect.texture_path = path;
                // 这里应该加载纹理并获取实际尺寸
                // 为了简化，使用默认尺寸
                rect.width = 64;
                rect.height = 64;
                textures.push_back(rect);
            }

            // 打包纹理
            std::vector<TextureRect> packed_textures;
            if (!PackTextures(textures, atlas_width, atlas_height, packed_textures))
            {
                return E_FAIL;
            }

            // 创建图集纹理
            ExSharedPtr<UITexture> atlas_texture;
            HRESULT hr = CreateAtlasTexture(packed_textures, atlas_width, atlas_height, atlas_texture);
            if (FAILED(hr))
            {
                return hr;
            }

            // 创建图集条目
            AtlasEntry entry;
            entry.atlas_texture = atlas_texture;
            entry.width = atlas_width;
            entry.height = atlas_height;
            entry.utilization = 0.0f;

            // 计算UV坐标
            for (const auto& rect : packed_textures)
            {
                DirectX::XMFLOAT4 uv;
                uv.x = static_cast<float>(rect.x) / atlas_width;
                uv.y = static_cast<float>(rect.y) / atlas_height;
                uv.z = static_cast<float>(rect.x + rect.width) / atlas_width;
                uv.w = static_cast<float>(rect.y + rect.height) / atlas_height;
                entry.texture_uvs[rect.texture_path] = uv;
            }

            m_atlases[atlas_name] = entry;
            m_stats.total_atlases++;
            m_stats.total_textures += static_cast<uint32_t>(texture_paths.size());

            return S_OK;
        }
        catch (const Exception& ex)
        {
            return ex.GetStatus();
        }
    }

    bool UITextureAtlasManager::GetTextureUV(const std::wstring& texture_path,
                                            const std::wstring& atlas_name,
                                            DirectX::XMFLOAT4& uv_rect)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        auto atlas_it = m_atlases.find(atlas_name);
        if (atlas_it == m_atlases.end())
            return false;

        auto uv_it = atlas_it->second.texture_uvs.find(texture_path);
        if (uv_it == atlas_it->second.texture_uvs.end())
            return false;

        uv_rect = uv_it->second;
        return true;
    }

    ExSharedPtr<UITexture> UITextureAtlasManager::GetAtlasTexture(const std::wstring& atlas_name)
    {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        auto it = m_atlases.find(atlas_name);
        return (it != m_atlases.end()) ? it->second.atlas_texture : nullptr;
    }

    HRESULT UITextureAtlasManager::OptimizeAtlasLayout(const std::wstring& atlas_name)
    {
        // 图集布局优化的实现
        // 这里可以实现更高效的纹理打包算法
        return S_OK;
    }

    bool UITextureAtlasManager::PackTextures(const std::vector<TextureRect>& textures,
                                            uint32_t atlas_width, uint32_t atlas_height,
                                            std::vector<TextureRect>& packed_textures)
    {
        // 简化的纹理打包算法（矩形装箱）
        packed_textures.clear();
        packed_textures.reserve(textures.size());

        uint32_t current_x = 0;
        uint32_t current_y = 0;
        uint32_t row_height = 0;

        for (const auto& texture : textures)
        {
            // 检查是否需要换行
            if (current_x + texture.width > atlas_width)
            {
                current_x = 0;
                current_y += row_height;
                row_height = 0;
            }

            // 检查是否超出图集高度
            if (current_y + texture.height > atlas_height)
            {
                return false; // 无法装下所有纹理
            }

            // 添加到打包结果
            TextureRect packed = texture;
            packed.x = current_x;
            packed.y = current_y;
            packed_textures.push_back(packed);

            current_x += texture.width;
            row_height = (std::max)(row_height, texture.height);
        }

        return true;
    }

    HRESULT UITextureAtlasManager::CreateAtlasTexture(const std::vector<TextureRect>& packed_textures,
                                                     uint32_t atlas_width, uint32_t atlas_height,
                                                     ExSharedPtr<UITexture>& atlas_texture)
    {
        // 创建图集纹理的实现
        // 这里应该创建一个新的纹理并将所有小纹理复制到其中
        
        atlas_texture = ExMakeShared<UITexture>();
        
        TextureDesc desc;
        desc.width = atlas_width;
        desc.height = atlas_height;
        desc.mip_levels = 1;
        desc.array_size = 1;
        desc.format = TextureFormat::R8G8B8A8_UNORM;
        desc.bind_flags = D3D11_BIND_SHADER_RESOURCE;

        return atlas_texture->Create(m_device, desc, nullptr);
    }

    // UIResourceStreamingManager 实现
    UIResourceStreamingManager::UIResourceStreamingManager()
        : m_memory_budget(512 * 1024 * 1024)
        , m_current_memory_usage(0)
        , m_shutdown_requested(false)
        , m_stats{}
    {
    }

    UIResourceStreamingManager::~UIResourceStreamingManager()
    {
        Shutdown();
    }

    HRESULT UIResourceStreamingManager::Initialize(uint32_t worker_thread_count,
                                                  uint64_t memory_budget)
    {
        try
        {
            m_memory_budget = memory_budget;
            m_shutdown_requested = false;
            m_stats = StreamingStats{};

            // 初始化LOD管理器
            HRESULT hr = m_lod_manager.Initialize();
            if (FAILED(hr))
                return hr;

            // 初始化图集管理器
            hr = m_atlas_manager.Initialize(nullptr); // 需要传入实际的设备
            if (FAILED(hr))
                return hr;

            // 创建工作线程
            m_worker_threads.reserve(worker_thread_count);
            for (uint32_t i = 0; i < worker_thread_count; ++i)
            {
                m_worker_threads.emplace_back(&UIResourceStreamingManager::WorkerThreadProc, this);
            }

            return S_OK;
        }
        catch (const Exception& ex)
        {
            return ex.GetStatus();
        }
    }

    void UIResourceStreamingManager::Shutdown()
    {
        // 请求关闭
        m_shutdown_requested = true;
        m_queue_cv.notify_all();

        // 等待所有工作线程结束
        for (auto& thread : m_worker_threads)
        {
            if (thread.joinable())
            {
                thread.join();
            }
        }

        m_worker_threads.clear();

        // 关闭子系统
        m_lod_manager.Shutdown();
        m_atlas_manager.Shutdown();

        // 清空资源
        std::lock_guard<std::mutex> lock(m_resources_mutex);
        m_resources.clear();
        
        // 清空队列
        std::lock_guard<std::mutex> queue_lock(m_queue_mutex);
        while (!m_load_queue.empty())
        {
            m_load_queue.pop();
        }

        m_stats = StreamingStats{};
    }

    void UIResourceStreamingManager::RegisterResource(const ResourceDesc& desc)
    {
        std::lock_guard<std::mutex> lock(m_resources_mutex);
        
        ResourceItem item;
        item.desc = desc;
        item.is_loaded = false;
        item.is_loading = false;
        item.reference_count = 0;

        m_resources[desc.file_path] = item;
        m_stats.registered_resources++;
    }

    std::future<ExSharedPtr<void>> UIResourceStreamingManager::RequestResource(const std::wstring& file_path,
                                                                              StreamingPriority priority)
    {
        std::lock_guard<std::mutex> lock(m_queue_mutex);
        
        LoadRequest request(file_path, priority);

        auto future = request.promise.get_future();
        m_load_queue.emplace(std::move(request));
        m_queue_cv.notify_one();
        
        return future;
    }

    void UIResourceStreamingManager::PreloadResource(const std::wstring& file_path,
                                                     StreamingPriority priority)
    {
        // 预加载资源（不返回future）
        RequestResource(file_path, priority);
    }

    void UIResourceStreamingManager::UnloadResource(const std::wstring& file_path)
    {
        std::lock_guard<std::mutex> lock(m_resources_mutex);
        
        auto it = m_resources.find(file_path);
        if (it != m_resources.end())
        {
            if (it->second.reference_count > 0)
            {
                it->second.reference_count--;
            }
            
            if (it->second.reference_count == 0)
            {
                m_current_memory_usage -= CalculateResourceSize(it->second);
                m_resources.erase(it);
                m_stats.loaded_resources--;
            }
        }
    }

    void UIResourceStreamingManager::UpdateVisibility(const std::wstring& file_path, bool is_visible, float distance)
    {
        std::lock_guard<std::mutex> lock(m_resources_mutex);
        
        auto it = m_resources.find(file_path);
        if (it != m_resources.end())
        {
            it->second.desc.is_visible = is_visible;
            it->second.desc.distance_from_camera = distance;
            it->second.desc.last_access_time = std::chrono::steady_clock::now();
        }
    }

    void UIResourceStreamingManager::GarbageCollect()
    {
        std::lock_guard<std::mutex> lock(m_resources_mutex);
        
        if (m_current_memory_usage <= m_memory_budget)
            return;

        // 执行LRU清理
        EvictLeastRecentlyUsed();
    }

    void UIResourceStreamingManager::SetMemoryBudget(uint64_t budget_bytes)
    {
        m_memory_budget = budget_bytes;
        m_stats.memory_budget = budget_bytes;
    }

    void UIResourceStreamingManager::WorkerThreadProc()
    {
        while (!m_shutdown_requested)
        {
            LoadRequest request;
            bool has_request = false;

            // 获取请求
            {
                std::unique_lock<std::mutex> lock(m_queue_mutex);
                m_queue_cv.wait(lock, [this] { 
                    return m_shutdown_requested || !m_load_queue.empty(); 
                });

                if (m_shutdown_requested)
                    break;

                if (!m_load_queue.empty())
                {
                    // 由于priority_queue不支持移动，我们需要重新构造
                    const auto& top_request = m_load_queue.top();
                    request = LoadRequest(top_request.file_path, top_request.priority);
                    // 移动promise
                    request.promise = std::move(const_cast<LoadRequest&>(top_request).promise);
                    request.request_time = top_request.request_time;
                    m_load_queue.pop();
                    has_request = true;
                }
            }

            // 处理请求
            if (has_request)
            {
                ProcessLoadRequest(request);
            }
        }
    }

    void UIResourceStreamingManager::ProcessLoadRequest(LoadRequest& request)
    {
        try
        {
            std::lock_guard<std::mutex> lock(m_resources_mutex);
            
            auto it = m_resources.find(request.file_path);
            if (it != m_resources.end())
            {
                ResourceItem& item = it->second;
                
                if (!item.is_loaded && !item.is_loading)
                {
                    item.is_loading = true;
                    
                    // 加载资源
                    ExSharedPtr<void> resource = LoadResource(item.desc);
                    
                    if (resource)
                    {
                        item.resource_data = resource;
                        item.is_loaded = true;
                        item.reference_count++;
                        m_current_memory_usage += CalculateResourceSize(item);
                        m_stats.loaded_resources++;
                        
                        request.promise.set_value(resource);
                    }
                    else
                    {
                        request.promise.set_exception(std::make_exception_ptr(
                            std::runtime_error("Failed to load resource")));
                    }
                    
                    item.is_loading = false;
                }
                else if (item.is_loaded)
                {
                    // 资源已加载
                    item.reference_count++;
                    request.promise.set_value(item.resource_data);
                }
            }
            else
            {
                request.promise.set_exception(std::make_exception_ptr(
                    std::runtime_error("Resource not registered")));
            }
        }
        catch (...)
        {
            request.promise.set_exception(std::current_exception());
        }
    }

    ExSharedPtr<void> UIResourceStreamingManager::LoadResource(const ResourceDesc& desc)
    {
        // 实际的资源加载实现
        // 这里应该根据资源类型加载不同的资源
        
        switch (desc.type)
        {
        case ResourceType::TEXTURE:
            // 加载纹理
            break;
        case ResourceType::MESH:
            // 加载网格
            break;
        case ResourceType::SHADER:
            // 加载着色器
            break;
        default:
            break;
        }
        
        // 为了简化，返回一个空的shared_ptr
        return ExSharedPtr<void>();
    }

    void UIResourceStreamingManager::EvictLeastRecentlyUsed()
    {
        // LRU清理算法
        auto now = std::chrono::steady_clock::now();
        
        std::vector<std::pair<std::chrono::steady_clock::time_point, std::wstring>> candidates;
        
        for (const auto& [path, item] : m_resources)
        {
            if (item.is_loaded && item.reference_count == 0)
            {
                candidates.emplace_back(item.desc.last_access_time, path);
            }
        }
        
        // 按访问时间排序
        std::sort(candidates.begin(), candidates.end());
        
        // 清理最久未使用的资源
        for (const auto& [time, path] : candidates)
        {
            if (m_current_memory_usage <= m_memory_budget)
                break;
                
            auto it = m_resources.find(path);
            if (it != m_resources.end())
            {
                m_current_memory_usage -= CalculateResourceSize(it->second);
                m_resources.erase(it);
                m_stats.loaded_resources--;
            }
        }
    }

    uint64_t UIResourceStreamingManager::CalculateResourceSize(const ResourceItem& item) const
    {
        // 简化的资源大小计算
        return item.desc.estimated_size;
    }
}
