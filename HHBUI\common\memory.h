﻿/**
** =====================================================================================
**
**       文件名称: memory.h
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】增强内存管理系统 - 现代化C++17内存管理框架 （声明文件）
**
**       主要功能:
**       - 高性能内存分配与释放管理
**       - 对齐内存分配支持（SSE/AVX优化）
**       - 零填充与安全内存操作
**       - 内存使用统计与监控
**       - 内存泄漏检测与调试支持
**       - 异常安全的内存管理
**       - 智能内存池集成
**
**       技术特性:
**       - 采用现代C++17标准与RAII设计
**       - 支持多种对齐方式（16/32/64字节对齐）
**       - 原子操作保证线程安全统计
**       - 异常安全保证与错误恢复机制
**       - 高性能内存分配算法
**       - 智能指针兼容设计
**       - 内存使用模式分析
**
**       更新记录:
**       2025-07-31 v2.0.0.0 : 1. 增强内存管理系统
**                             2. 添加对齐分配支持
**                             3. 实现安全内存操作
**                             4. 添加内存统计功能
**
** =====================================================================================
*/
#pragma once
#include <memory>
#include <atomic>
#include <cstring>
#include <algorithm>

namespace HHBUI
{
	// 内存对齐枚举
	enum class MemoryAlignment : size_t
	{
		Default = sizeof(void*),    // 默认对齐（指针大小）
		SSE = 16,                   // SSE指令集对齐
		AVX = 32,                   // AVX指令集对齐
		AVX512 = 64                 // AVX-512指令集对齐
	};

	// 内存统计信息
	struct MemoryStats
	{
		std::atomic<size_t> total_allocated{ 0 };      // 总分配字节数
		std::atomic<size_t> total_freed{ 0 };          // 总释放字节数
		std::atomic<size_t> current_usage{ 0 };        // 当前使用字节数
		std::atomic<size_t> peak_usage{ 0 };           // 峰值使用字节数
		std::atomic<size_t> allocation_count{ 0 };     // 分配次数
		std::atomic<size_t> free_count{ 0 };           // 释放次数
		std::atomic<size_t> alignment_count{ 0 };      // 对齐分配次数
	};
	/**
	 * @brief 内存_申请
	 * 申请一段内存
	 * @param size 申请的内存大小
	 * @return 返回分配的内存地址
	 * @attention 使用完毕后，需要调用{ExMemFree}释放内存
	 */
	LPVOID ExMemAlloc(size_t size);

	/**
	 * @brief 内存_重分配
	 * 重新分配一段内存
	 * @param ptr 旧内存地址
	 * @param new_size 新的内存大小
	 * @return 返回新的内存地址
	 * @attention 使用完毕后，需要调用{ExMemFree}释放内存
	 */
	LPVOID ExMemReAlloc(LPVOID ptr, size_t new_size);

	/**
	 * @brief 内存_释放
	 * 释放一段内存
	 * @param ptr 内存地址
	 */
	void ExMemFree(LPVOID ptr);

	/**
	 * @brief 内存_取尺寸
	 * 获取一段内存的尺寸
	 * @param ptr 内存地址
	 * @return 返回内存大小
	 */
	size_t ExMemGetSize(LPVOID ptr);

	/**
	 * @brief 内存_对齐申请
	 * 申请一段对齐的内存
	 * @param size 申请的内存大小
	 * @param alignment 对齐字节数
	 * @return 返回分配的内存地址
	 * @attention 使用完毕后，需要调用{ExMemFreeAligned}释放内存
	 */
	LPVOID ExMemAllocAligned(size_t size, MemoryAlignment alignment = MemoryAlignment::Default);

	/**
	 * @brief 内存_对齐释放
	 * 释放一段对齐分配的内存
	 * @param ptr 内存地址
	 */
	void ExMemFreeAligned(LPVOID ptr);

	/**
	 * @brief 内存_零填充申请
	 * 申请一段零填充的内存
	 * @param size 申请的内存大小
	 * @return 返回分配的内存地址
	 * @attention 使用完毕后，需要调用{ExMemFree}释放内存
	 */
	LPVOID ExMemAllocZero(size_t size);

	/**
	 * @brief 内存_安全拷贝
	 * 安全地拷贝内存内容
	 * @param dest 目标内存地址
	 * @param dest_size 目标内存大小
	 * @param src 源内存地址
	 * @param src_size 源内存大小
	 * @return 成功返回S_OK，失败返回错误码
	 */
	HRESULT ExMemCopySafe(LPVOID dest, size_t dest_size, LPCVOID src, size_t src_size);

	/**
	 * @brief 内存_安全设置
	 * 安全地设置内存内容
	 * @param dest 目标内存地址
	 * @param dest_size 目标内存大小
	 * @param value 设置的值
	 * @return 成功返回S_OK，失败返回错误码
	 */
	HRESULT ExMemSetSafe(LPVOID dest, size_t dest_size, int value);

	/**
	 * @brief 内存_安全清零
	 * 安全地清零内存内容
	 * @param dest 目标内存地址
	 * @param dest_size 目标内存大小
	 * @return 成功返回S_OK，失败返回错误码
	 */
	HRESULT ExMemZeroSafe(LPVOID dest, size_t dest_size);

	/**
	 * @brief 获取内存统计信息
	 * @return 返回内存统计信息
	 */
	const MemoryStats& ExMemGetStats();

	/**
	 * @brief 重置内存统计信息
	 */
	void ExMemResetStats();

	/**
	 * @brief 检查内存是否对齐
	 * @param ptr 内存地址
	 * @param alignment 对齐字节数
	 * @return 对齐返回true，否则返回false
	 */
	bool ExMemIsAligned(LPCVOID ptr, size_t alignment);

	// 兼容性宏定义
#define ExAlloc(size) ExMemAlloc(size)
#define ExreAlloc(ptr, new_size) ExMemReAlloc(ptr, new_size)
#define ExFree(ptr) ExMemFree(ptr)
#define ExSafeRelease(pPointer)			{ if (pPointer) { pPointer->Release(); pPointer = NULL; } }
#define ExSafeDelete(pPointer)			{ try { if (pPointer) {delete pPointer;} } catch (...) { assert(false); } pPointer = NULL; }

	// 新增增强宏定义
#define ExAllocAligned(size, align) ExMemAllocAligned(size, align)
#define ExAllocZero(size) ExMemAllocZero(size)
#define ExFreeAligned(ptr) ExMemFreeAligned(ptr)

}
