/**
** =====================================================================================
**
**       文件名称: render_integration.cpp
**       创建时间: 2025-07-28
**       文件描述: 【HHBUI】渲染框架集成系统 - 高级渲染管线集成与协调框架 （实现文件）
**
**       主要功能:
**       - 高级渲染管线集成与协调实现
**       - 多渲染API统一管理与调度
**       - 渲染上下文扩展与增强功能
**       - 高性能渲染资源管理实现
**       - 渲染性能监控与调试集成
**       - 混合渲染模式协调管理
**       - 跨平台渲染抽象层实现
**
**       技术特性:
**       - 采用现代C++17标准与多渲染API
**       - COM接口规范与智能指针管理
**       - 异常安全保证与错误恢复机制
**       - 高性能渲染管线调度算法
**       - 多线程安全的资源管理实现
**       - 智能渲染状态缓存与优化
**       - 实时性能监控与调试诊断
**
**       更新记录:
**       2025-07-28 v1.0.0.0 : 1. 实现渲染框架集成系统
**                             2. 完成多渲染API统一管理
**                             3. 实现渲染上下文扩展
**                             4. 支持高性能资源管理
**                             5. 完成性能监控集成
**                             6. 集成混合渲染协调
**                             7. 确保跨平台兼容性
**
** =====================================================================================
**/

#include "pch.h"
#include "renderd2d.h"
#include "dx11_render_manager.h"
#include "render_profiler.h"
#include "gdi_plus_integration.h"
#include "common/Exception.h"

namespace HHBUI
{
	// 全局性能监控器实例
	UIRenderProfiler* g_render_profiler = nullptr;
	UIRenderDebugger* g_render_debugger = nullptr;

	// UIDrawContext扩展实现
	HRESULT UIDrawContext::InitAdvancedRendering()
	{
		try
		{
			if (ToList.advanced_features_enabled)
				return S_OK;

			// 创建高级渲染管理器
			UIDx11RenderFactory factory;
			throw_if_failed(
				factory.CreateRenderManager(&ToList.advanced_render_manager),
				L"创建高级渲染管理器失败"
			);

			// 初始化渲染管理器
			throw_if_failed(
				ToList.advanced_render_manager->Initialize(RenderType::HYBRID),
				L"初始化高级渲染管理器失败"
			);

			// 创建子管理器
			auto* dx11_manager = static_cast<UIDx11RenderManager*>(ToList.advanced_render_manager);
			ToList.shader_manager = new UIShaderManager(
				dx11_manager->GetD3D11Device(), 
				dx11_manager->GetD3D11DeviceContext()
			);

			ToList.buffer_manager = new UIBufferManager(
				dx11_manager->GetD3D11Device(), 
				dx11_manager->GetD3D11DeviceContext()
			);

			ToList.input_layout_manager = new UIInputLayoutManager(
				dx11_manager->GetD3D11Device()
			);

			// 创建性能监控器
			g_render_profiler = new UIRenderProfiler();
			throw_if_failed(
				g_render_profiler->Initialize(
					dx11_manager->GetD3D11Device(), 
					dx11_manager->GetD3D11DeviceContext()
				),
				L"初始化性能监控器失败"
			);

			// 创建调试器
			g_render_debugger = new UIRenderDebugger();
			throw_if_failed(
				g_render_debugger->Initialize(
					dx11_manager->GetD3D11Device(), 
					dx11_manager->GetD3D11DeviceContext()
				),
				L"初始化渲染调试器失败"
			);

			// 添加基础性能计数器
			g_render_profiler->AddCounter("DrawCalls", ProfilerCounterType::DRAW_CALLS);
			g_render_profiler->AddCounter("Triangles", ProfilerCounterType::TRIANGLES);
			g_render_profiler->AddCounter("Vertices", ProfilerCounterType::VERTICES);
			g_render_profiler->AddCounter("TextureSwitches", ProfilerCounterType::TEXTURE_SWITCHES);
			g_render_profiler->AddCounter("ShaderSwitches", ProfilerCounterType::SHADER_SWITCHES);

			// 初始化GDI+集成
			g_gdi_plus_integration = new UIGdiPlusIntegration();
			if (dx11_manager->GetD2D1DeviceContext())
			{
				g_gdi_plus_integration->Initialize(dx11_manager->GetD2D1DeviceContext());
			}

			g_gdi_plus_resource_manager = new UIGdiPlusResourceManager();

			g_hybrid_render_coordinator = new UIHybridRenderCoordinator();
			if (ToList.d2d_dc && ToList.d2d_gdiInterop)
			{
				HDC gdi_dc = nullptr;
				if (SUCCEEDED(ToList.d2d_gdiInterop->GetDC(D2D1_DC_INITIALIZE_MODE_COPY, &gdi_dc)))
				{
					g_hybrid_render_coordinator->Initialize(ToList.d2d_dc, gdi_dc);
				}
			}

			ToList.advanced_features_enabled = true;
			return S_OK;
		}
		catch_default({});
	}

	void UIDrawContext::ShutdownAdvancedRendering()
	{
		if (!ToList.advanced_features_enabled)
			return;

		// 清理性能监控器
		if (g_render_profiler)
		{
			g_render_profiler->Shutdown();
			delete g_render_profiler;
			g_render_profiler = nullptr;
		}

		// 清理调试器
		if (g_render_debugger)
		{
			g_render_debugger->Shutdown();
			delete g_render_debugger;
			g_render_debugger = nullptr;
		}

		// 清理GDI+集成
		if (g_hybrid_render_coordinator)
		{
			delete g_hybrid_render_coordinator;
			g_hybrid_render_coordinator = nullptr;
		}

		if (g_gdi_plus_resource_manager)
		{
			delete g_gdi_plus_resource_manager;
			g_gdi_plus_resource_manager = nullptr;
		}

		if (g_gdi_plus_integration)
		{
			g_gdi_plus_integration->Shutdown();
			delete g_gdi_plus_integration;
			g_gdi_plus_integration = nullptr;
		}

		// 清理子管理器
		if (ToList.input_layout_manager)
		{
			ToList.input_layout_manager->Cleanup();
			delete ToList.input_layout_manager;
			ToList.input_layout_manager = nullptr;
		}

		if (ToList.buffer_manager)
		{
			ToList.buffer_manager->Cleanup();
			delete ToList.buffer_manager;
			ToList.buffer_manager = nullptr;
		}

		if (ToList.shader_manager)
		{
			ToList.shader_manager->Cleanup();
			delete ToList.shader_manager;
			ToList.shader_manager = nullptr;
		}

		// 清理高级渲染管理器
		if (ToList.advanced_render_manager)
		{
			ToList.advanced_render_manager->Shutdown();
			ToList.advanced_render_manager->Release();
			ToList.advanced_render_manager = nullptr;
		}

		ToList.advanced_features_enabled = false;
	}

	IRenderManager* UIDrawContext::GetAdvancedRenderManager()
	{
		return ToList.advanced_render_manager;
	}

	HRESULT UIDrawContext::EnableAdvancedFeatures(bool enable)
	{
		if (enable && !ToList.advanced_features_enabled)
		{
			return InitAdvancedRendering();
		}
		else if (!enable && ToList.advanced_features_enabled)
		{
			ShutdownAdvancedRendering();
			return S_OK;
		}
		return S_OK;
	}

	bool UIDrawContext::IsAdvancedFeaturesEnabled()
	{
		return ToList.advanced_features_enabled;
	}

	HRESULT UIDrawContext::CreateCustomShader(ShaderType type, LPCWSTR source_code, 
		LPCSTR entry_point, IShader** shader)
	{
		if (!ToList.shader_manager || !shader)
			return E_INVALIDARG;

		return ToList.shader_manager->CompileShaderFromSource(type, source_code, entry_point, shader);
	}

	HRESULT UIDrawContext::LoadShaderFromFile(ShaderType type, LPCWSTR file_path, 
		LPCSTR entry_point, IShader** shader)
	{
		if (!ToList.shader_manager || !shader)
			return E_INVALIDARG;

		return ToList.shader_manager->LoadShaderFromFile(type, file_path, entry_point, shader);
	}

	HRESULT UIDrawContext::CreateVertexBuffer(const void* vertices, uint32_t vertex_count, 
		uint32_t vertex_size, bool dynamic, IBuffer** buffer)
	{
		if (!ToList.buffer_manager || !buffer)
			return E_INVALIDARG;

		return ToList.buffer_manager->CreateVertexBuffer(vertices, vertex_count, vertex_size, dynamic, buffer);
	}

	HRESULT UIDrawContext::CreateIndexBuffer(const uint32_t* indices, uint32_t index_count, 
		bool dynamic, IBuffer** buffer)
	{
		if (!ToList.buffer_manager || !buffer)
			return E_INVALIDARG;

		return ToList.buffer_manager->CreateIndexBuffer(indices, index_count, dynamic, buffer);
	}

	HRESULT UIDrawContext::CreateConstantBuffer(uint32_t size, bool dynamic, IBuffer** buffer)
	{
		if (!ToList.buffer_manager || !buffer)
			return E_INVALIDARG;

		return ToList.buffer_manager->CreateConstantBuffer(size, dynamic, buffer);
	}

	HRESULT UIDrawContext::CreateTexture2D(uint32_t width, uint32_t height, DXGI_FORMAT format, 
		const void* initial_data, bool render_target, ITexture** texture)
	{
		if (!ToList.advanced_render_manager || !texture)
			return E_INVALIDARG;

		Microsoft::WRL::ComPtr<ITexture> tex;
		HRESULT hr = ToList.advanced_render_manager->CreateTexture(&tex);
		if (FAILED(hr)) return hr;

		hr = tex->Create2D(width, height, format, initial_data, render_target, true);
		if (FAILED(hr)) return hr;

		*texture = tex.Detach();
		return S_OK;
	}

	HRESULT UIDrawContext::LoadTextureFromFile(LPCWSTR file_path, ITexture** texture)
	{
		if (!ToList.advanced_render_manager || !texture)
			return E_INVALIDARG;

		Microsoft::WRL::ComPtr<ITexture> tex;
		HRESULT hr = ToList.advanced_render_manager->CreateTexture(&tex);
		if (FAILED(hr)) return hr;

		hr = tex->LoadFromFile(file_path);
		if (FAILED(hr)) return hr;

		*texture = tex.Detach();
		return S_OK;
	}

	HRESULT UIDrawContext::CreateRenderState(IRenderState** render_state)
	{
		if (!ToList.advanced_render_manager || !render_state)
			return E_INVALIDARG;

		return ToList.advanced_render_manager->CreateRenderState(render_state);
	}

	HRESULT UIDrawContext::SetBlendMode(bool enable, D3D11_BLEND src_blend, D3D11_BLEND dest_blend)
	{
		Microsoft::WRL::ComPtr<IRenderState> state;
		HRESULT hr = CreateRenderState(&state);
		if (FAILED(hr)) return hr;

		hr = state->SetBlendState(enable, src_blend, dest_blend);
		if (FAILED(hr)) return hr;

		return state->Apply();
	}

	HRESULT UIDrawContext::SetDepthTest(bool enable, bool write_enable, D3D11_COMPARISON_FUNC func)
	{
		Microsoft::WRL::ComPtr<IRenderState> state;
		HRESULT hr = CreateRenderState(&state);
		if (FAILED(hr)) return hr;

		hr = state->SetDepthStencilState(enable, write_enable, func);
		if (FAILED(hr)) return hr;

		return state->Apply();
	}

	HRESULT UIDrawContext::DrawPrimitive(D3D11_PRIMITIVE_TOPOLOGY topology, uint32_t vertex_count, uint32_t start_vertex)
	{
		if (!ToList.advanced_render_manager)
			return E_INVALIDARG;

		// 设置图元拓扑
		auto* context = ToList.advanced_render_manager->GetD3D11DeviceContext();
		context->IASetPrimitiveTopology(topology);

		// 绘制
		HRESULT hr = ToList.advanced_render_manager->Draw(vertex_count, start_vertex);
		
		// 更新统计
		if (SUCCEEDED(hr) && g_render_profiler)
		{
			g_render_profiler->IncrementCounter("DrawCalls");
			g_render_profiler->UpdateCounter("Vertices", vertex_count);
			
			// 估算三角形数量
			uint32_t triangles = 0;
			switch (topology)
			{
			case D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST:
				triangles = vertex_count / 3;
				break;
			case D3D11_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP:
				triangles = vertex_count - 2;
				break;
			}
			g_render_profiler->UpdateCounter("Triangles", triangles);
		}

		return hr;
	}

	HRESULT UIDrawContext::DrawIndexedPrimitive(D3D11_PRIMITIVE_TOPOLOGY topology, uint32_t index_count, uint32_t start_index, uint32_t base_vertex)
	{
		if (!ToList.advanced_render_manager)
			return E_INVALIDARG;

		// 设置图元拓扑
		auto* context = ToList.advanced_render_manager->GetD3D11DeviceContext();
		context->IASetPrimitiveTopology(topology);

		// 绘制
		HRESULT hr = ToList.advanced_render_manager->DrawIndexed(index_count, start_index, base_vertex);
		
		// 更新统计
		if (SUCCEEDED(hr) && g_render_profiler)
		{
			g_render_profiler->IncrementCounter("DrawCalls");
			
			// 估算三角形数量
			uint32_t triangles = 0;
			switch (topology)
			{
			case D3D11_PRIMITIVE_TOPOLOGY_TRIANGLELIST:
				triangles = index_count / 3;
				break;
			case D3D11_PRIMITIVE_TOPOLOGY_TRIANGLESTRIP:
				triangles = index_count - 2;
				break;
			}
			g_render_profiler->UpdateCounter("Triangles", triangles);
		}

		return hr;
	}

	const RenderStats& UIDrawContext::GetRenderStats()
	{
		if (ToList.advanced_render_manager)
		{
			return ToList.advanced_render_manager->GetRenderStats();
		}
		return ToList.render_stats;
	}

	void UIDrawContext::ResetRenderStats()
	{
		if (ToList.advanced_render_manager)
		{
			ToList.advanced_render_manager->ResetRenderStats();
		}
		if (g_render_profiler)
		{
			g_render_profiler->ResetStats();
		}
		memset(&ToList.render_stats, 0, sizeof(ToList.render_stats));
	}

	uint64_t UIDrawContext::GetGPUMemoryUsage()
	{
		if (g_render_profiler)
		{
			return g_render_profiler->GetGPUMemoryUsage();
		}
		return 0;
	}

	float UIDrawContext::GetFrameTime()
	{
		if (g_render_profiler)
		{
			auto* counter = g_render_profiler->GetCounter("FrameTime");
			return counter ? static_cast<float>(counter->current_value) : 0.0f;
		}
		return 0.0f;
	}

	float UIDrawContext::GetGPUTime()
	{
		if (g_render_profiler)
		{
			auto* counter = g_render_profiler->GetCounter("GPUTime");
			return counter ? static_cast<float>(counter->current_value) : 0.0f;
		}
		return 0.0f;
	}

	// 高级功能扩展接口
	HRESULT UIDrawContext::BeginAdvancedFrame()
	{
		if (!ToList.advanced_render_manager)
			return E_FAIL;

		RENDER_PROFILE_BEGIN_FRAME();
		return ToList.advanced_render_manager->BeginFrame();
	}

	HRESULT UIDrawContext::EndAdvancedFrame()
	{
		if (!ToList.advanced_render_manager)
			return E_FAIL;

		HRESULT hr = ToList.advanced_render_manager->EndFrame();
		RENDER_PROFILE_END_FRAME();
		return hr;
	}

	HRESULT UIDrawContext::PresentAdvanced(bool vsync)
	{
		if (!ToList.advanced_render_manager)
			return E_FAIL;

		return ToList.advanced_render_manager->Present(vsync);
	}

	HRESULT UIDrawContext::SetAdvancedViewport(float x, float y, float width, float height)
	{
		if (!ToList.advanced_render_manager)
			return E_FAIL;

		return ToList.advanced_render_manager->SetViewport(x, y, width, height);
	}

	HRESULT UIDrawContext::ClearAdvancedRenderTarget(float r, float g, float b, float a)
	{
		if (!ToList.advanced_render_manager)
			return E_FAIL;

		return ToList.advanced_render_manager->ClearRenderTarget(r, g, b, a);
	}

	// GDI+集成便捷接口
	HRESULT UIDrawContext::ConvertGdiPlusBitmapToD2D(Gdiplus::Bitmap* gdi_bitmap, ID2D1Bitmap** d2d_bitmap)
	{
		if (!g_gdi_plus_integration || !gdi_bitmap || !d2d_bitmap)
			return E_INVALIDARG;

		return g_gdi_plus_integration->GetCachedD2DBitmap(gdi_bitmap, d2d_bitmap);
	}

	HRESULT UIDrawContext::BeginHybridRendering()
	{
		if (!g_hybrid_render_coordinator)
			return E_FAIL;

		return g_hybrid_render_coordinator->BeginHybridSession();
	}

	HRESULT UIDrawContext::EndHybridRendering()
	{
		if (!g_hybrid_render_coordinator)
			return E_FAIL;

		return g_hybrid_render_coordinator->EndHybridSession();
	}

	HRESULT UIDrawContext::SwitchToGdiPlusMode()
	{
		if (!g_hybrid_render_coordinator)
			return E_FAIL;

		return g_hybrid_render_coordinator->SwitchToGdiPlusMode();
	}

	HRESULT UIDrawContext::SwitchToD2DMode()
	{
		if (!g_hybrid_render_coordinator)
			return E_FAIL;

		return g_hybrid_render_coordinator->SwitchToD2DMode();
	}

	// 性能监控便捷接口
	std::string UIDrawContext::GetPerformanceReport()
	{
		if (!g_render_profiler)
			return "Performance profiler not available";

		return g_render_profiler->GenerateReport();
	}

	HRESULT UIDrawContext::ExportPerformanceData(LPCWSTR file_path)
	{
		if (!g_render_profiler || !file_path)
			return E_INVALIDARG;

		return g_render_profiler->ExportToFile(file_path);
	}

	void UIDrawContext::SetDebugMode(bool enable)
	{
		ToList.debug_mode = enable;

		if (ToList.advanced_render_manager)
		{
			ToList.advanced_render_manager->SetDebugMode(enable);
		}

		if (g_render_profiler)
		{
			g_render_profiler->SetEnabled(enable);
		}
	}

	bool UIDrawContext::IsDebugMode()
	{
		return ToList.debug_mode;
	}
}
