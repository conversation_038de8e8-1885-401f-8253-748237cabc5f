/**
** =====================================================================================
**
**       文件名称: enhanced_engine_macros.h
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】增强引擎宏定义 - 便捷API宏定义集合 （声明文件）
**
** =====================================================================================
**/

#pragma once

// 包含所有增强引擎头文件，使用各自头文件中已定义的宏
#include "enhanced_engine_integration.h"
#include "render_batch.h"
#include "texture_manager.h"
#include "render_command_queue.h"
#include "antialiasing.h"
#include "post_processing.h"
#include "render_pipeline.h"
#include "resource_streaming.h"
#include "enhanced_animation.h"
#include "enhanced_layout.h"
#include "performance_monitor.h"

namespace HHBUI
{
    // =====================================================================================
    // 增强引擎宏定义统一包含点
    // =====================================================================================
    
    // 注意：所有宏定义已在各自的头文件中定义，此文件仅作为统一包含点
    // 避免重复定义导致的编译警告
    // 
    // 增强引擎集成宏定义在: enhanced_engine_integration.h
    // - ENHANCED_ENGINE_INIT(device, context, width, height, config)
    // - ENHANCED_ENGINE_UPDATE(delta)
    // - ENHANCED_ENGINE_BEGIN_FRAME(id)
    // - ENHANCED_ENGINE_END_FRAME()
    // - ENHANCED_ENGINE_SHUTDOWN()
    // - ENHANCED_ENGINE_SCOPED_FRAME()
    //
    // 渲染批处理宏定义在: render_batch.h
    // - RENDER_BATCH_BEGIN_FRAME()
    // - RENDER_BATCH_END_FRAME()
    // - RENDER_BATCH_EXECUTE()
    // - RENDER_BATCH_ADD(batch)
    // - RENDER_BATCH_SCOPED_FRAME()
    //
    // 抗锯齿宏定义在: antialiasing.h
    // - AA_SET_TYPE(type, quality)
    // - AA_END(output_rtv)
    // - AA_SCOPED()
    //
    // 后处理宏定义在: post_processing.h
    // - POST_PROCESS_ADD_EFFECT(effect, quality)
    // - POST_PROCESS_EXECUTE(input_srv, output_rtv)
    //
    // 渲染命令队列宏定义在: render_command_queue.h
    // - RENDER_COMMAND_BEGIN_FRAME(frame_id)
    // - RENDER_COMMAND_END_FRAME()
    // - RENDER_COMMAND_EXECUTE()
    //
    // 动画宏定义在: enhanced_animation.h
    // - ANIMATION_CREATE_CLIP(name)
    // - ANIMATION_CREATE_INSTANCE(clip)
    // - ANIMATION_UPDATE(delta_time)
    //
    // 布局宏定义在: enhanced_layout.h
    // - LAYOUT_CREATE_FLEXBOX()
    // - LAYOUT_CREATE_RESPONSIVE()
    // - LAYOUT_CREATE_CONSTRAINT()
    // - LAYOUT_UPDATE_ALL()
    //
    // 性能监控宏定义在: performance_monitor.h
    // - PERF_RECORD(metric, value)
    // - PERF_BEGIN_FRAME()
    // - PERF_END_FRAME()
    // - MEMORY_RECORD_ALLOC(category, size, ptr)
    // - MEMORY_RECORD_FREE(ptr)
    // - FRAMERATE_RECORD(frame_time_ms)
    //
    // 资源流式管理宏定义在: resource_streaming.h
    // - RESOURCE_REGISTER(desc)
    // - RESOURCE_REQUEST(file_path, priority)
    // - RESOURCE_PRELOAD(file_path)
    // - RESOURCE_UPDATE_VISIBILITY(file_path, visible, distance)
    //
    // 纹理管理宏定义在: texture_manager.h
    // - TEXTURE_LOAD(file_path, priority)

    // =====================================================================================
    // 组合宏定义 - 提供高级便捷操作
    // =====================================================================================

    /// 完整的增强引擎帧处理
    #ifndef ENHANCED_ENGINE_FULL_FRAME
    #define ENHANCED_ENGINE_FULL_FRAME(frame_id) \
        do { \
            ENHANCED_ENGINE_BEGIN_FRAME(frame_id); \
            RENDER_BATCH_BEGIN_FRAME(); \
            RENDER_COMMAND_BEGIN_FRAME(frame_id); \
            PERF_BEGIN_FRAME(); \
        } while(0)
    #endif

    /// 完整的增强引擎帧结束
    #ifndef ENHANCED_ENGINE_FULL_FRAME_END
    #define ENHANCED_ENGINE_FULL_FRAME_END() \
        do { \
            PERF_END_FRAME(); \
            RENDER_COMMAND_END_FRAME(); \
            RENDER_BATCH_END_FRAME(); \
            ENHANCED_ENGINE_END_FRAME(); \
        } while(0)
    #endif

    /// RAII完整帧管理器
    class EnhancedEngineFullFrameScope
    {
    public:
        explicit EnhancedEngineFullFrameScope(uint32_t frame_id) : m_frame_id(frame_id)
        {
            ENHANCED_ENGINE_FULL_FRAME(m_frame_id);
        }
        
        ~EnhancedEngineFullFrameScope()
        {
            ENHANCED_ENGINE_FULL_FRAME_END();
        }
        
    private:
        uint32_t m_frame_id;
    };

    /// 作用域完整帧管理
    #ifndef ENHANCED_ENGINE_SCOPED_FULL_FRAME
    #define ENHANCED_ENGINE_SCOPED_FULL_FRAME(frame_id) \
        EnhancedEngineFullFrameScope _full_frame_scope(frame_id)
    #endif

    /// 快速初始化所有增强引擎组件
    #ifndef ENHANCED_ENGINE_QUICK_INIT
    #define ENHANCED_ENGINE_QUICK_INIT(device, context, width, height) \
        do { \
            UIEnhancedEngineConfig config; \
            config.enable_antialiasing = true; \
            config.enable_post_processing = true; \
            config.enable_performance_monitoring = true; \
            config.enable_resource_streaming = true; \
            config.enable_enhanced_animation = true; \
            config.enable_enhanced_layout = true; \
            ENHANCED_ENGINE_INIT(device, context, width, height, config); \
        } while(0)
    #endif

} // namespace HHBUI
