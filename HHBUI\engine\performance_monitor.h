/**
** =====================================================================================
**
**       文件名称: performance_monitor.h
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】性能监控系统 - 实时性能分析与自动优化框架 （声明文件）
**
**       主要功能:
**       - 实时性能指标监控
**       - 内存使用分析
**       - 帧率稳定性监控
**       - 自适应渲染质量调节
**       - 智能资源管理
**       - 动态LOD调整
**       - 性能瓶颈检测与优化建议
**
**       技术特性:
**       - 采用现代C++17标准与高精度计时
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能监控算法
**       - 多线程安全的数据收集
**       - 自适应优化策略
**       - 实时性能数据可视化
**
**       更新记录:
**       2025-07-31 v1.0.0.0 : 1. 创建性能监控系统
**                             2. 实现实时性能监控
**                             3. 添加内存使用分析
**                             4. 完成帧率稳定性监控
**                             5. 集成自适应优化
**                             6. 实现智能资源管理
**                             7. 确保多线程安全性
**
** =====================================================================================
**/

#pragma once

// 防止Windows宏干扰
#ifndef NOMINMAX
#define NOMINMAX
#endif

#include "render_api.h"
#include "common/smart_ptr.hpp"
#include <vector>
#include <unordered_map>
#include <queue>
#include <mutex>
#include <atomic>
#include <chrono>
#include <memory>
#include <string>
#include <cfloat>

namespace HHBUI
{
    /// 性能指标类型
    enum class PerformanceMetric : uint32_t
    {
        FRAME_TIME = 0,             // 帧时间
        FPS,                        // 帧率
        CPU_USAGE,                  // CPU使用率
        GPU_USAGE,                  // GPU使用率
        MEMORY_USAGE,               // 内存使用量
        GPU_MEMORY_USAGE,           // GPU内存使用量
        DRAW_CALLS,                 // 绘制调用数
        TRIANGLES,                  // 三角形数量
        TEXTURE_MEMORY,             // 纹理内存
        BUFFER_MEMORY,              // 缓冲区内存
        SHADER_SWITCHES,            // 着色器切换次数
        RENDER_TARGET_SWITCHES,     // 渲染目标切换次数
        CUSTOM                      // 自定义指标
    };

    /// 性能警告级别
    enum class PerformanceWarningLevel : uint32_t
    {
        INFO = 0,
        WARNING,
        CRITICAL
    };

    /// 性能数据点
    struct PerformanceDataPoint
    {
        std::chrono::steady_clock::time_point timestamp;
        float value;
        
        PerformanceDataPoint() : value(0.0f) {}
        PerformanceDataPoint(float v) : timestamp(std::chrono::steady_clock::now()), value(v) {}
    };

    /// 性能统计信息
    struct PerformanceStats
    {
        float current_value;
        float average_value;
        float min_value;
        float max_value;
        float variance;
        uint32_t sample_count;
        
        PerformanceStats() : current_value(0.0f), average_value(0.0f), 
                           min_value(FLT_MAX), max_value(-FLT_MAX),
                           variance(0.0f), sample_count(0) {}
    };

    /// 性能阈值
    struct PerformanceThreshold
    {
        float warning_threshold;
        float critical_threshold;
        bool higher_is_better;      // true表示数值越高越好，false表示越低越好
        
        PerformanceThreshold() : warning_threshold(0.0f), critical_threshold(0.0f), 
                               higher_is_better(true) {}
        PerformanceThreshold(float warning, float critical, bool higher_better = true)
            : warning_threshold(warning), critical_threshold(critical), 
              higher_is_better(higher_better) {}
    };

    /// 性能监控器
    class UIPerformanceMonitor
    {
    public:
        UIPerformanceMonitor();
        ~UIPerformanceMonitor();

        /// 初始化性能监控器
        HRESULT Initialize(uint32_t history_size = 1000);

        /// 关闭性能监控器
        void Shutdown();

        /// 开始帧监控
        void BeginFrame();

        /// 结束帧监控
        void EndFrame();

        /// 记录性能指标
        void RecordMetric(PerformanceMetric metric, float value);

        /// 设置性能阈值
        void SetThreshold(PerformanceMetric metric, const PerformanceThreshold& threshold);

        /// 获取性能统计
        const PerformanceStats& GetStats(PerformanceMetric metric) const;

        /// 获取历史数据
        const std::vector<PerformanceDataPoint>& GetHistory(PerformanceMetric metric) const;

        /// 检查性能警告
        PerformanceWarningLevel CheckWarningLevel(PerformanceMetric metric) const;

        /// 获取性能报告
        std::string GeneratePerformanceReport() const;

        /// 导出性能数据
        HRESULT ExportPerformanceData(LPCWSTR file_path) const;

        /// 启用/禁用监控
        void SetEnabled(bool enabled) { m_enabled = enabled; }
        bool IsEnabled() const { return m_enabled; }

        /// 设置历史数据大小
        void SetHistorySize(uint32_t size);

        /// 清空历史数据
        void ClearHistory();

    private:
        struct MetricData
        {
            std::vector<PerformanceDataPoint> history;
            PerformanceStats stats;
            PerformanceThreshold threshold;
            uint32_t history_index;
        };

        std::unordered_map<PerformanceMetric, MetricData> m_metrics;
        std::chrono::steady_clock::time_point m_frame_start_time;
        uint32_t m_history_size;
        std::atomic<bool> m_enabled;
        mutable std::mutex m_mutex;

        void UpdateStats(MetricData& data);
        void AddDataPoint(MetricData& data, float value);
    };

    /// 内存使用分析器
    class UIMemoryAnalyzer
    {
    public:
        UIMemoryAnalyzer();
        ~UIMemoryAnalyzer();

        /// 初始化内存分析器
        HRESULT Initialize();

        /// 关闭内存分析器
        void Shutdown();

        /// 记录内存分配
        void RecordAllocation(const std::string& category, size_t size, const void* ptr);

        /// 记录内存释放
        void RecordDeallocation(const void* ptr);

        /// 获取内存使用统计
        struct MemoryUsageStats
        {
            size_t total_allocated;
            size_t total_freed;
            size_t current_usage;
            size_t peak_usage;
            uint32_t allocation_count;
            uint32_t deallocation_count;
            uint32_t active_allocations;
            std::unordered_map<std::string, size_t> category_usage;
        };
        const MemoryUsageStats& GetStats() const { return m_stats; }

        /// 检测内存泄漏
        std::vector<std::pair<const void*, size_t>> DetectMemoryLeaks() const;

        /// 生成内存报告
        std::string GenerateMemoryReport() const;

        /// 设置内存预算
        void SetMemoryBudget(size_t budget_bytes) { m_memory_budget = budget_bytes; }

        /// 检查内存预算
        bool IsMemoryBudgetExceeded() const { return m_stats.current_usage > m_memory_budget; }

    private:
        struct AllocationInfo
        {
            std::string category;
            size_t size;
            std::chrono::steady_clock::time_point timestamp;
        };

        std::unordered_map<const void*, AllocationInfo> m_allocations;
        MemoryUsageStats m_stats;
        size_t m_memory_budget;
        mutable std::mutex m_mutex;

        void UpdateCategoryUsage(const std::string& category, int64_t size_delta);
    };

    /// 帧率稳定性监控器
    class UIFrameRateMonitor
    {
    public:
        UIFrameRateMonitor();
        ~UIFrameRateMonitor();

        /// 初始化帧率监控器
        HRESULT Initialize(uint32_t sample_window = 60);

        /// 关闭帧率监控器
        void Shutdown();

        /// 记录帧时间
        void RecordFrameTime(float frame_time_ms);

        /// 获取当前帧率
        float GetCurrentFPS() const { return m_current_fps; }

        /// 获取平均帧率
        float GetAverageFPS() const { return m_average_fps; }

        /// 获取帧率稳定性（0.0-1.0，1.0表示完全稳定）
        float GetFrameRateStability() const;

        /// 获取帧时间方差
        float GetFrameTimeVariance() const { return m_frame_time_variance; }

        /// 检测帧率下降
        bool IsFrameRateDropDetected() const;

        /// 获取统计信息
        struct FrameRateStats
        {
            float current_fps;
            float average_fps;
            float min_fps;
            float max_fps;
            float stability;
            float frame_time_variance;
            uint32_t dropped_frames;
            uint32_t total_frames;
        };
        const FrameRateStats& GetStats() const { return m_stats; }

    private:
        std::vector<float> m_frame_times;
        uint32_t m_sample_window;
        uint32_t m_current_index;
        
        float m_current_fps;
        float m_average_fps;
        float m_frame_time_variance;
        
        FrameRateStats m_stats;
        mutable std::mutex m_mutex;

        void UpdateStatistics();
        float CalculateVariance() const;
    };

    /// 自动优化管理器
    class UIAutoOptimizationManager
    {
    public:
        UIAutoOptimizationManager();
        ~UIAutoOptimizationManager();

        /// 初始化自动优化管理器
        HRESULT Initialize(UIPerformanceMonitor* performance_monitor,
                          UIMemoryAnalyzer* memory_analyzer,
                          UIFrameRateMonitor* framerate_monitor);

        /// 关闭自动优化管理器
        void Shutdown();

        /// 更新自动优化
        void Update();

        /// 启用/禁用自动优化
        void SetEnabled(bool enabled) { m_enabled = enabled; }
        bool IsEnabled() const { return m_enabled; }

        /// 设置目标帧率
        void SetTargetFrameRate(float target_fps) { m_target_fps = target_fps; }

        /// 设置内存预算
        void SetMemoryBudget(size_t budget_bytes) { m_memory_budget = budget_bytes; }

        /// 获取优化统计
        struct OptimizationStats
        {
            uint32_t quality_adjustments;
            uint32_t lod_adjustments;
            uint32_t memory_cleanups;
            uint32_t texture_compressions;
            float performance_improvement;
        };
        const OptimizationStats& GetStats() const { return m_stats; }

    private:
        UIPerformanceMonitor* m_performance_monitor;
        UIMemoryAnalyzer* m_memory_analyzer;
        UIFrameRateMonitor* m_framerate_monitor;
        
        bool m_enabled;
        float m_target_fps;
        size_t m_memory_budget;
        
        OptimizationStats m_stats;
        std::chrono::steady_clock::time_point m_last_optimization_time;

        void OptimizeRenderingQuality();
        void OptimizeLODLevels();
        void OptimizeMemoryUsage();
        void OptimizeTextureQuality();
        
        bool ShouldOptimize() const;
        float GetPerformanceScore() const;
    };

    /// 性能分析面板
    class UIPerformancePanel
    {
    public:
        UIPerformancePanel();
        ~UIPerformancePanel();

        /// 初始化性能面板
        HRESULT Initialize(UIPerformanceMonitor* performance_monitor,
                          UIMemoryAnalyzer* memory_analyzer,
                          UIFrameRateMonitor* framerate_monitor);

        /// 关闭性能面板
        void Shutdown();

        /// 渲染性能面板
        void Render(ID3D11DeviceContext* context, const ExRectF& panel_rect);

        /// 设置可见性
        void SetVisible(bool visible) { m_visible = visible; }
        bool IsVisible() const { return m_visible; }

        /// 设置更新频率
        void SetUpdateFrequency(float updates_per_second) { m_update_frequency = updates_per_second; }

    private:
        UIPerformanceMonitor* m_performance_monitor;
        UIMemoryAnalyzer* m_memory_analyzer;
        UIFrameRateMonitor* m_framerate_monitor;
        
        bool m_visible;
        float m_update_frequency;
        std::chrono::steady_clock::time_point m_last_update_time;

        void RenderFrameRateGraph(const ExRectF& rect);
        void RenderMemoryUsageGraph(const ExRectF& rect);
        void RenderPerformanceMetrics(const ExRectF& rect);
        void RenderOptimizationStatus(const ExRectF& rect);
    };

    /// 全局性能监控系统实例
    extern UIPerformanceMonitor* g_performance_monitor;
    extern UIMemoryAnalyzer* g_memory_analyzer;
    extern UIFrameRateMonitor* g_framerate_monitor;
    extern UIAutoOptimizationManager* g_auto_optimization_manager;
    extern UIPerformancePanel* g_performance_panel;

    /// 便捷宏定义
    #define PERF_BEGIN_FRAME() if(g_performance_monitor) g_performance_monitor->BeginFrame()
    #define PERF_END_FRAME() if(g_performance_monitor) g_performance_monitor->EndFrame()
    #define PERF_RECORD(metric, value) if(g_performance_monitor) g_performance_monitor->RecordMetric(metric, value)
    #define MEMORY_RECORD_ALLOC(category, size, ptr) if(g_memory_analyzer) g_memory_analyzer->RecordAllocation(category, size, ptr)
    #define MEMORY_RECORD_FREE(ptr) if(g_memory_analyzer) g_memory_analyzer->RecordDeallocation(ptr)
    #define FRAMERATE_RECORD(time_ms) if(g_framerate_monitor) g_framerate_monitor->RecordFrameTime(time_ms)
}
