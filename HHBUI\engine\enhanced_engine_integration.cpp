/**
** =====================================================================================
**
**       文件名称: enhanced_engine_integration.cpp
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】增强引擎集成系统 - 统一的增强功能初始化与管理框架 （实现文件）
**
** =====================================================================================
**/

#include "pch.h"
#include "enhanced_engine_integration.h"
#include "render_batch.h"
#include "texture_manager.h"
#include "render_command_queue.h"
#include "antialiasing.h"
#include "post_processing.h"
#include "render_pipeline.h"
#include "resource_streaming.h"
#include "enhanced_animation.h"
#include "enhanced_layout.h"
#include "performance_monitor.h"
#include "common/Exception.h"

namespace HHBUI
{
    // 全局增强引擎集成管理器实例
    UIEnhancedEngineIntegration* g_enhanced_engine_integration = nullptr;

    UIEnhancedEngineIntegration::UIEnhancedEngineIntegration()
        : m_device(nullptr)
        , m_context(nullptr)
        , m_width(0)
        , m_height(0)
        , m_initialized(false)
        , m_stats{}
    {
    }

    UIEnhancedEngineIntegration::~UIEnhancedEngineIntegration()
    {
        Shutdown();
    }

    HRESULT UIEnhancedEngineIntegration::Initialize(ID3D11Device* device, ID3D11DeviceContext* context,
                                                   uint32_t width, uint32_t height,
                                                   const EnhancedEngineConfig& config)
    {
        try
        {
            if (!device || !context)
                return E_INVALIDARG;

            if (m_initialized)
                return S_OK;

            m_device = device;
            m_context = context;
            m_width = width;
            m_height = height;
            m_config = config;

            m_initialization_start_time = std::chrono::steady_clock::now();

            // 注册所有系统
            RegisterAllSystems();

            // 按依赖顺序初始化系统
            auto init_order = GetInitializationOrder();
            for (auto system_type : init_order)
            {
                auto& system_info = m_systems[system_type];
                if (system_info.enabled && system_info.init_func)
                {
                    system_info.state = SystemState::INITIALIZING;
                    
                    HRESULT hr = system_info.init_func();
                    if (SUCCEEDED(hr))
                    {
                        system_info.state = SystemState::INITIALIZED;
                        m_stats.initialized_systems++;
                    }
                    else
                    {
                        system_info.state = SystemState::SYSTEM_ERROR;
                        system_info.error_message = "初始化失败";
                        m_stats.error_systems++;
                    }
                }
            }

            auto init_end_time = std::chrono::steady_clock::now();
            auto init_duration = std::chrono::duration_cast<std::chrono::microseconds>(init_end_time - m_initialization_start_time);
            m_stats.total_initialization_time_ms = init_duration.count() / 1000.0f;

            m_initialized = true;
            return S_OK;
        }
        catch (const Exception& ex)
        {
            return ex.GetStatus();
        }
    }

    void UIEnhancedEngineIntegration::Shutdown()
    {
        if (!m_initialized)
            return;

        // 按相反顺序关闭系统
        auto shutdown_order = GetShutdownOrder();
        for (auto system_type : shutdown_order)
        {
            auto& system_info = m_systems[system_type];
            if (system_info.state == SystemState::INITIALIZED && system_info.shutdown_func)
            {
                system_info.state = SystemState::SHUTTING_DOWN;
                system_info.shutdown_func();
                system_info.state = SystemState::SHUTDOWN;
            }
        }

        m_systems.clear();
        m_initialized = false;
        m_device = nullptr;
        m_context = nullptr;
        memset(&m_stats, 0, sizeof(m_stats));
    }

    void UIEnhancedEngineIntegration::Update(float delta_time)
    {
        if (!m_initialized)
            return;

        auto update_start_time = std::chrono::steady_clock::now();

        m_stats.active_systems = 0;
        for (auto& [system_type, system_info] : m_systems)
        {
            if (system_info.state == SystemState::INITIALIZED && system_info.enabled && system_info.update_func)
            {
                auto system_start_time = std::chrono::steady_clock::now();
                
                system_info.update_func(delta_time);
                system_info.last_update_time = system_start_time;
                
                auto system_end_time = std::chrono::steady_clock::now();
                auto system_duration = std::chrono::duration_cast<std::chrono::microseconds>(system_end_time - system_start_time);
                system_info.update_time_ms = system_duration.count() / 1000.0f;
                
                m_stats.system_update_times_ms[system_type] = system_info.update_time_ms;
                m_stats.active_systems++;
            }
        }

        auto update_end_time = std::chrono::steady_clock::now();
        auto update_duration = std::chrono::duration_cast<std::chrono::microseconds>(update_end_time - update_start_time);
        m_stats.total_update_time_ms = update_duration.count() / 1000.0f;
    }

    void UIEnhancedEngineIntegration::BeginFrame(uint32_t frame_id)
    {
        if (!m_initialized)
            return;

        // 通知所有系统开始新帧
        RENDER_BATCH_BEGIN_FRAME();
        RENDER_COMMAND_BEGIN_FRAME(frame_id);
        PERF_BEGIN_FRAME();
    }

    void UIEnhancedEngineIntegration::EndFrame()
    {
        if (!m_initialized)
            return;

        // 通知所有系统结束帧
        RENDER_BATCH_EXECUTE();
        RENDER_BATCH_END_FRAME();
        RENDER_COMMAND_EXECUTE();
        RENDER_COMMAND_END_FRAME();
        PERF_END_FRAME();
    }

    SystemState UIEnhancedEngineIntegration::GetSystemState(EnhancedSystemType system) const
    {
        auto it = m_systems.find(system);
        return (it != m_systems.end()) ? it->second.state : SystemState::UNINITIALIZED;
    }

    std::unordered_map<EnhancedSystemType, SystemState> UIEnhancedEngineIntegration::GetAllSystemStates() const
    {
        std::unordered_map<EnhancedSystemType, SystemState> states;
        for (const auto& [system_type, system_info] : m_systems)
        {
            states[system_type] = system_info.state;
        }
        return states;
    }

    void UIEnhancedEngineIntegration::SetSystemEnabled(EnhancedSystemType system, bool enabled)
    {
        auto it = m_systems.find(system);
        if (it != m_systems.end())
        {
            it->second.enabled = enabled;
        }
    }

    bool UIEnhancedEngineIntegration::IsSystemEnabled(EnhancedSystemType system) const
    {
        auto it = m_systems.find(system);
        return (it != m_systems.end()) ? it->second.enabled : false;
    }

    std::string UIEnhancedEngineIntegration::GetSystemError(EnhancedSystemType system) const
    {
        auto it = m_systems.find(system);
        return (it != m_systems.end()) ? it->second.error_message : "";
    }

    std::string UIEnhancedEngineIntegration::GenerateSystemReport() const
    {
        std::ostringstream report;
        report << "=== HHBUI Enhanced Engine System Report ===\n";
        report << "Initialized Systems: " << m_stats.initialized_systems << "\n";
        report << "Active Systems: " << m_stats.active_systems << "\n";
        report << "Error Systems: " << m_stats.error_systems << "\n";
        report << "Total Initialization Time: " << m_stats.total_initialization_time_ms << " ms\n";
        report << "Total Update Time: " << m_stats.total_update_time_ms << " ms\n\n";

        report << "System States:\n";
        for (const auto& [system_type, system_info] : m_systems)
        {
            report << "  System " << static_cast<int>(system_type) << ": ";
            switch (system_info.state)
            {
            case SystemState::UNINITIALIZED: report << "UNINITIALIZED"; break;
            case SystemState::INITIALIZING: report << "INITIALIZING"; break;
            case SystemState::INITIALIZED: report << "INITIALIZED"; break;
            case SystemState::SYSTEM_ERROR: report << "SYSTEM_ERROR"; break;
            case SystemState::SHUTTING_DOWN: report << "SHUTTING_DOWN"; break;
            case SystemState::SHUTDOWN: report << "SHUTDOWN"; break;
            }
            report << " (Enabled: " << (system_info.enabled ? "Yes" : "No") << ")";
            if (!system_info.error_message.empty())
            {
                report << " - Error: " << system_info.error_message;
            }
            report << "\n";
        }

        return report.str();
    }

    void UIEnhancedEngineIntegration::RegisterAllSystems()
    {
        // 注册渲染批处理系统
        SystemInfo render_batch_info;
        render_batch_info.state = SystemState::UNINITIALIZED;
        render_batch_info.enabled = true;
        render_batch_info.init_func = [this]() { return InitializeRenderBatchSystem(); };
        render_batch_info.shutdown_func = [this]() { ShutdownRenderBatchSystem(); };
        render_batch_info.update_func = [this](float dt) { UpdateRenderBatchSystem(dt); };
        RegisterSystem(EnhancedSystemType::RENDER_BATCH, render_batch_info);

        // 注册纹理管理系统
        SystemInfo texture_manager_info;
        texture_manager_info.state = SystemState::UNINITIALIZED;
        texture_manager_info.enabled = true;
        texture_manager_info.init_func = [this]() { return InitializeTextureManagerSystem(); };
        texture_manager_info.shutdown_func = [this]() { ShutdownTextureManagerSystem(); };
        texture_manager_info.update_func = [this](float dt) { UpdateTextureManagerSystem(dt); };
        RegisterSystem(EnhancedSystemType::TEXTURE_MANAGER, texture_manager_info);

        // 注册命令队列系统
        SystemInfo command_queue_info;
        command_queue_info.state = SystemState::UNINITIALIZED;
        command_queue_info.enabled = true;
        command_queue_info.init_func = [this]() { return InitializeCommandQueueSystem(); };
        command_queue_info.shutdown_func = [this]() { ShutdownCommandQueueSystem(); };
        command_queue_info.update_func = [this](float dt) { UpdateCommandQueueSystem(dt); };
        RegisterSystem(EnhancedSystemType::COMMAND_QUEUE, command_queue_info);

        // 注册抗锯齿系统
        SystemInfo antialiasing_info;
        antialiasing_info.state = SystemState::UNINITIALIZED;
        antialiasing_info.enabled = true;
        antialiasing_info.init_func = [this]() { return InitializeAntiAliasingSystem(); };
        antialiasing_info.shutdown_func = [this]() { ShutdownAntiAliasingSystem(); };
        antialiasing_info.update_func = [this](float dt) { UpdateAntiAliasingSystem(dt); };
        RegisterSystem(EnhancedSystemType::ANTIALIASING, antialiasing_info);

        // 注册后处理系统
        SystemInfo post_processing_info;
        post_processing_info.state = SystemState::UNINITIALIZED;
        post_processing_info.enabled = true;
        post_processing_info.init_func = [this]() { return InitializePostProcessingSystem(); };
        post_processing_info.shutdown_func = [this]() { ShutdownPostProcessingSystem(); };
        post_processing_info.update_func = [this](float dt) { UpdatePostProcessingSystem(dt); };
        RegisterSystem(EnhancedSystemType::POST_PROCESSING, post_processing_info);

        // 注册渲染管线系统
        SystemInfo render_pipeline_info;
        render_pipeline_info.state = SystemState::UNINITIALIZED;
        render_pipeline_info.enabled = true;
        render_pipeline_info.init_func = [this]() { return InitializeRenderPipelineSystem(); };
        render_pipeline_info.shutdown_func = [this]() { ShutdownRenderPipelineSystem(); };
        render_pipeline_info.update_func = [this](float dt) { UpdateRenderPipelineSystem(dt); };
        RegisterSystem(EnhancedSystemType::RENDER_PIPELINE, render_pipeline_info);

        // 注册资源流式系统
        SystemInfo resource_streaming_info;
        resource_streaming_info.state = SystemState::UNINITIALIZED;
        resource_streaming_info.enabled = true;
        resource_streaming_info.init_func = [this]() { return InitializeResourceStreamingSystem(); };
        resource_streaming_info.shutdown_func = [this]() { ShutdownResourceStreamingSystem(); };
        resource_streaming_info.update_func = [this](float dt) { UpdateResourceStreamingSystem(dt); };
        RegisterSystem(EnhancedSystemType::RESOURCE_STREAMING, resource_streaming_info);

        // 注册增强动画系统
        SystemInfo enhanced_animation_info;
        enhanced_animation_info.state = SystemState::UNINITIALIZED;
        enhanced_animation_info.enabled = true;
        enhanced_animation_info.init_func = [this]() { return InitializeEnhancedAnimationSystem(); };
        enhanced_animation_info.shutdown_func = [this]() { ShutdownEnhancedAnimationSystem(); };
        enhanced_animation_info.update_func = [this](float dt) { UpdateEnhancedAnimationSystem(dt); };
        RegisterSystem(EnhancedSystemType::ENHANCED_ANIMATION, enhanced_animation_info);

        // 注册增强布局系统
        SystemInfo enhanced_layout_info;
        enhanced_layout_info.state = SystemState::UNINITIALIZED;
        enhanced_layout_info.enabled = true;
        enhanced_layout_info.init_func = [this]() { return InitializeEnhancedLayoutSystem(); };
        enhanced_layout_info.shutdown_func = [this]() { ShutdownEnhancedLayoutSystem(); };
        enhanced_layout_info.update_func = [this](float dt) { UpdateEnhancedLayoutSystem(dt); };
        RegisterSystem(EnhancedSystemType::ENHANCED_LAYOUT, enhanced_layout_info);

        // 注册性能监控系统
        SystemInfo performance_monitor_info;
        performance_monitor_info.state = SystemState::UNINITIALIZED;
        performance_monitor_info.enabled = true;
        performance_monitor_info.init_func = [this]() { return InitializePerformanceMonitorSystem(); };
        performance_monitor_info.shutdown_func = [this]() { ShutdownPerformanceMonitorSystem(); };
        performance_monitor_info.update_func = [this](float dt) { UpdatePerformanceMonitorSystem(dt); };
        RegisterSystem(EnhancedSystemType::PERFORMANCE_MONITOR, performance_monitor_info);
    }

    void UIEnhancedEngineIntegration::RegisterSystem(EnhancedSystemType type, const SystemInfo& info)
    {
        m_systems[type] = info;
    }

    std::vector<EnhancedSystemType> UIEnhancedEngineIntegration::GetInitializationOrder() const
    {
        // 按依赖关系排序的初始化顺序
        return {
            EnhancedSystemType::PERFORMANCE_MONITOR,    // 首先初始化监控系统
            EnhancedSystemType::TEXTURE_MANAGER,        // 纹理管理
            EnhancedSystemType::RENDER_BATCH,           // 渲染批处理
            EnhancedSystemType::COMMAND_QUEUE,          // 命令队列
            EnhancedSystemType::ANTIALIASING,           // 抗锯齿
            EnhancedSystemType::POST_PROCESSING,        // 后处理
            EnhancedSystemType::RENDER_PIPELINE,        // 渲染管线
            EnhancedSystemType::RESOURCE_STREAMING,     // 资源流式
            EnhancedSystemType::ENHANCED_ANIMATION,     // 增强动画
            EnhancedSystemType::ENHANCED_LAYOUT         // 增强布局
        };
    }

    std::vector<EnhancedSystemType> UIEnhancedEngineIntegration::GetShutdownOrder() const
    {
        // 与初始化相反的顺序
        auto init_order = GetInitializationOrder();
        std::reverse(init_order.begin(), init_order.end());
        return init_order;
    }

    bool UIEnhancedEngineIntegration::CheckSystemDependencies(EnhancedSystemType system) const
    {
        // 简化的依赖检查
        switch (system)
        {
        case EnhancedSystemType::RENDER_PIPELINE:
            return GetSystemState(EnhancedSystemType::RENDER_BATCH) == SystemState::INITIALIZED &&
                   GetSystemState(EnhancedSystemType::ANTIALIASING) == SystemState::INITIALIZED;
        case EnhancedSystemType::POST_PROCESSING:
            return GetSystemState(EnhancedSystemType::TEXTURE_MANAGER) == SystemState::INITIALIZED;
        default:
            return true;
        }
    }

    // 系统初始化函数实现
    HRESULT UIEnhancedEngineIntegration::InitializeRenderBatchSystem()
    {
        if (!g_batch_manager)
        {
            g_batch_manager = new UIRenderBatchManager();
        }
        return g_batch_manager->Initialize(m_device, m_context);
    }

    HRESULT UIEnhancedEngineIntegration::InitializeTextureManagerSystem()
    {
        if (!g_texture_manager)
        {
            g_texture_manager = new UITextureManager();
        }
        return g_texture_manager->Initialize(m_device, m_context);
    }

    HRESULT UIEnhancedEngineIntegration::InitializeCommandQueueSystem()
    {
        if (!g_render_command_queue)
        {
            g_render_command_queue = new UIRenderCommandQueue();
        }
        return g_render_command_queue->Initialize(m_config.command_queue_config.max_commands);
    }

    HRESULT UIEnhancedEngineIntegration::InitializeAntiAliasingSystem()
    {
        if (!g_antialiasing_manager)
        {
            g_antialiasing_manager = new UIAntiAliasingManager();
        }
        return g_antialiasing_manager->Initialize(m_device, m_context, m_width, m_height);
    }

    HRESULT UIEnhancedEngineIntegration::InitializePostProcessingSystem()
    {
        if (!g_post_process_pipeline)
        {
            g_post_process_pipeline = new UIPostProcessPipeline();
        }
        return g_post_process_pipeline->Initialize(m_device, m_context, m_width, m_height);
    }

    HRESULT UIEnhancedEngineIntegration::InitializeRenderPipelineSystem()
    {
        if (!g_render_pipeline_manager)
        {
            g_render_pipeline_manager = new UIRenderPipelineManager();
        }
        return g_render_pipeline_manager->Initialize(m_device, m_context, m_width, m_height);
    }

    HRESULT UIEnhancedEngineIntegration::InitializeResourceStreamingSystem()
    {
        if (!g_resource_streaming_manager)
        {
            g_resource_streaming_manager = new UIResourceStreamingManager();
        }
        return g_resource_streaming_manager->Initialize(
            m_config.streaming_config.worker_thread_count,
            m_config.streaming_config.memory_budget
        );
    }

    HRESULT UIEnhancedEngineIntegration::InitializeEnhancedAnimationSystem()
    {
        if (!g_enhanced_animation_manager)
        {
            g_enhanced_animation_manager = new UIEnhancedAnimationManager();
        }
        return g_enhanced_animation_manager->Initialize();
    }

    HRESULT UIEnhancedEngineIntegration::InitializeEnhancedLayoutSystem()
    {
        if (!g_enhanced_layout_manager)
        {
            g_enhanced_layout_manager = new UIEnhancedLayoutManager();
        }
        return g_enhanced_layout_manager->Initialize();
    }

    HRESULT UIEnhancedEngineIntegration::InitializePerformanceMonitorSystem()
    {
        if (!g_performance_monitor)
        {
            g_performance_monitor = new UIPerformanceMonitor();
        }
        if (!g_memory_analyzer)
        {
            g_memory_analyzer = new UIMemoryAnalyzer();
        }
        if (!g_framerate_monitor)
        {
            g_framerate_monitor = new UIFrameRateMonitor();
        }

        HRESULT hr = g_performance_monitor->Initialize(m_config.monitor_config.history_size);
        if (FAILED(hr)) return hr;

        hr = g_memory_analyzer->Initialize();
        if (FAILED(hr)) return hr;

        return g_framerate_monitor->Initialize();
    }

    // 系统关闭函数实现
    void UIEnhancedEngineIntegration::ShutdownRenderBatchSystem()
    {
        if (g_batch_manager)
        {
            g_batch_manager->Shutdown();
            delete g_batch_manager;
            g_batch_manager = nullptr;
        }
    }

    void UIEnhancedEngineIntegration::ShutdownTextureManagerSystem()
    {
        if (g_texture_manager)
        {
            g_texture_manager->Shutdown();
            delete g_texture_manager;
            g_texture_manager = nullptr;
        }
    }

    void UIEnhancedEngineIntegration::ShutdownCommandQueueSystem()
    {
        if (g_render_command_queue)
        {
            g_render_command_queue->Shutdown();
            delete g_render_command_queue;
            g_render_command_queue = nullptr;
        }
    }

    void UIEnhancedEngineIntegration::ShutdownAntiAliasingSystem()
    {
        if (g_antialiasing_manager)
        {
            g_antialiasing_manager->Shutdown();
            delete g_antialiasing_manager;
            g_antialiasing_manager = nullptr;
        }
    }

    void UIEnhancedEngineIntegration::ShutdownPostProcessingSystem()
    {
        if (g_post_process_pipeline)
        {
            g_post_process_pipeline->Shutdown();
            delete g_post_process_pipeline;
            g_post_process_pipeline = nullptr;
        }
    }

    void UIEnhancedEngineIntegration::ShutdownRenderPipelineSystem()
    {
        if (g_render_pipeline_manager)
        {
            g_render_pipeline_manager->Shutdown();
            delete g_render_pipeline_manager;
            g_render_pipeline_manager = nullptr;
        }
    }

    void UIEnhancedEngineIntegration::ShutdownResourceStreamingSystem()
    {
        if (g_resource_streaming_manager)
        {
            g_resource_streaming_manager->Shutdown();
            delete g_resource_streaming_manager;
            g_resource_streaming_manager = nullptr;
        }
    }

    void UIEnhancedEngineIntegration::ShutdownEnhancedAnimationSystem()
    {
        if (g_enhanced_animation_manager)
        {
            g_enhanced_animation_manager->Shutdown();
            delete g_enhanced_animation_manager;
            g_enhanced_animation_manager = nullptr;
        }
    }

    void UIEnhancedEngineIntegration::ShutdownEnhancedLayoutSystem()
    {
        if (g_enhanced_layout_manager)
        {
            g_enhanced_layout_manager->Shutdown();
            delete g_enhanced_layout_manager;
            g_enhanced_layout_manager = nullptr;
        }
    }

    void UIEnhancedEngineIntegration::ShutdownPerformanceMonitorSystem()
    {
        if (g_performance_monitor)
        {
            g_performance_monitor->Shutdown();
            delete g_performance_monitor;
            g_performance_monitor = nullptr;
        }
        if (g_memory_analyzer)
        {
            g_memory_analyzer->Shutdown();
            delete g_memory_analyzer;
            g_memory_analyzer = nullptr;
        }
        if (g_framerate_monitor)
        {
            g_framerate_monitor->Shutdown();
            delete g_framerate_monitor;
            g_framerate_monitor = nullptr;
        }
    }

    // 系统更新函数实现
    void UIEnhancedEngineIntegration::UpdateRenderBatchSystem(float delta_time)
    {
        // 渲染批处理系统通常在渲染时更新，这里可以添加统计更新
    }

    void UIEnhancedEngineIntegration::UpdateTextureManagerSystem(float delta_time)
    {
        if (g_texture_manager)
        {
            g_texture_manager->CleanupCache();
        }
    }

    void UIEnhancedEngineIntegration::UpdateCommandQueueSystem(float delta_time)
    {
        // 命令队列系统通常在渲染时更新
    }

    void UIEnhancedEngineIntegration::UpdateAntiAliasingSystem(float delta_time)
    {
        // 抗锯齿系统通常在渲染时更新
    }

    void UIEnhancedEngineIntegration::UpdatePostProcessingSystem(float delta_time)
    {
        // 后处理系统通常在渲染时更新
    }

    void UIEnhancedEngineIntegration::UpdateRenderPipelineSystem(float delta_time)
    {
        // 渲染管线系统通常在渲染时更新
    }

    void UIEnhancedEngineIntegration::UpdateResourceStreamingSystem(float delta_time)
    {
        if (g_resource_streaming_manager)
        {
            g_resource_streaming_manager->GarbageCollect();
        }
    }

    void UIEnhancedEngineIntegration::UpdateEnhancedAnimationSystem(float delta_time)
    {
        if (g_enhanced_animation_manager)
        {
            g_enhanced_animation_manager->UpdateAnimations(delta_time);
        }
    }

    void UIEnhancedEngineIntegration::UpdateEnhancedLayoutSystem(float delta_time)
    {
        if (g_enhanced_layout_manager)
        {
            g_enhanced_layout_manager->UpdateAllLayouts();
        }
    }

    void UIEnhancedEngineIntegration::UpdatePerformanceMonitorSystem(float delta_time)
    {
        // 性能监控系统在BeginFrame/EndFrame中更新
    }
}
