/**
** =====================================================================================
**
**       文件名称: enhanced_layout.h
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】增强布局系统 - 现代化布局引擎与响应式设计框架 （声明文件）
**
**       主要功能:
**       - 优化的布局计算算法
**       - Flexbox布局支持
**       - 响应式布局系统
**       - 约束布局引擎
**       - 自适应布局策略
**       - 布局性能优化
**       - 布局调试与可视化
**
**       技术特性:
**       - 采用现代C++17标准与高性能算法
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能布局计算引擎
**       - 多线程安全的布局管理
**       - 自适应布局优化策略
**       - 实时性能监控与统计分析
**
**       更新记录:
**       2025-07-31 v1.0.0.0 : 1. 创建增强布局系统
**                             2. 实现优化布局算法
**                             3. 添加Flexbox支持
**                             4. 完成响应式布局
**                             5. 集成约束布局引擎
**                             6. 实现自适应策略
**                             7. 确保性能优化
**
** =====================================================================================
**/

#pragma once
#include "element/layout.h"
#include "common/smart_ptr.hpp"
#include <vector>
#include <unordered_map>
#include <functional>
#include <memory>
#include <cstdint>

namespace HHBUI
{
    // 前向声明
    class UIControl;
    struct ExRectF;

    /// Flexbox方向
    enum class FlexDirection : uint32_t
    {
        ROW = 0,                    // 水平方向
        ROW_REVERSE,                // 水平反向
        COLUMN,                     // 垂直方向
        COLUMN_REVERSE              // 垂直反向
    };

    /// Flexbox换行
    enum class FlexWrap : uint32_t
    {
        NO_WRAP = 0,                // 不换行
        WRAP,                       // 换行
        WRAP_REVERSE                // 反向换行
    };

    /// Flexbox对齐方式
    enum class FlexAlign : uint32_t
    {
        FLEX_START = 0,             // 起始对齐
        FLEX_END,                   // 结束对齐
        CENTER,                     // 居中对齐
        SPACE_BETWEEN,              // 两端对齐
        SPACE_AROUND,               // 环绕对齐
        SPACE_EVENLY,               // 均匀对齐
        STRETCH                     // 拉伸对齐
    };

    /// 响应式断点
    enum class ResponsiveBreakpoint : uint32_t
    {
        XS = 0,                     // 超小屏幕 (<576px)
        SM,                         // 小屏幕 (≥576px)
        MD,                         // 中等屏幕 (≥768px)
        LG,                         // 大屏幕 (≥992px)
        XL,                         // 超大屏幕 (≥1200px)
        XXL                         // 超超大屏幕 (≥1400px)
    };

    /// 约束类型
    enum class ConstraintType : uint32_t
    {
        EQUAL = 0,                  // 等于
        LESS_THAN_OR_EQUAL,         // 小于等于
        GREATER_THAN_OR_EQUAL       // 大于等于
    };

    /// 约束属性
    enum class ConstraintAttribute : uint32_t
    {
        LEFT = 0,
        TOP,
        RIGHT,
        BOTTOM,
        WIDTH,
        HEIGHT,
        CENTER_X,
        CENTER_Y,
        BASELINE
    };

    /// Flexbox项目属性
    struct FlexItemProperties
    {
        float flex_grow;            // 放大比例
        float flex_shrink;          // 缩小比例
        float flex_basis;           // 基础大小
        FlexAlign align_self;       // 自身对齐方式
        int32_t order;              // 排序顺序
        
        FlexItemProperties() : flex_grow(0.0f), flex_shrink(1.0f), flex_basis(-1.0f),
                              align_self(FlexAlign::STRETCH), order(0) {}
    };

    /// Flexbox容器属性
    struct FlexContainerProperties
    {
        FlexDirection direction;    // 主轴方向
        FlexWrap wrap;              // 换行方式
        FlexAlign justify_content;  // 主轴对齐
        FlexAlign align_items;      // 交叉轴对齐
        FlexAlign align_content;    // 多行对齐
        float gap;                  // 间距
        
        FlexContainerProperties() : direction(FlexDirection::ROW), wrap(FlexWrap::NO_WRAP),
                                   justify_content(FlexAlign::FLEX_START), 
                                   align_items(FlexAlign::STRETCH),
                                   align_content(FlexAlign::STRETCH), gap(0.0f) {}
    };

    /// 响应式属性
    struct ResponsiveProperties
    {
        std::unordered_map<ResponsiveBreakpoint, ExRectF> sizes;
        std::unordered_map<ResponsiveBreakpoint, bool> visibility;
        std::unordered_map<ResponsiveBreakpoint, FlexItemProperties> flex_properties;
        
        ResponsiveProperties() = default;
    };

    /// 布局约束
    struct LayoutConstraint
    {
        UIControl* first_item;      // 第一个控件
        ConstraintAttribute first_attribute;
        ConstraintType relation;
        UIControl* second_item;     // 第二个控件（可为空）
        ConstraintAttribute second_attribute;
        float multiplier;           // 乘数
        float constant;             // 常数
        float priority;             // 优先级
        
        LayoutConstraint() : first_item(nullptr), first_attribute(ConstraintAttribute::LEFT),
                           relation(ConstraintType::EQUAL), second_item(nullptr),
                           second_attribute(ConstraintAttribute::LEFT), multiplier(1.0f),
                           constant(0.0f), priority(1000.0f) {}
    };

    /// Flexbox布局引擎
    class UIFlexboxLayoutEngine
    {
    public:
        UIFlexboxLayoutEngine();
        ~UIFlexboxLayoutEngine();

        /// 设置容器属性
        void SetContainerProperties(const FlexContainerProperties& properties);

        /// 设置项目属性
        void SetItemProperties(UIControl* item, const FlexItemProperties& properties);

        /// 添加项目
        void AddItem(UIControl* item);

        /// 移除项目
        void RemoveItem(UIControl* item);

        /// 计算布局
        void CalculateLayout(const ExRectF& container_rect);

        /// 获取项目计算后的矩形
        ExRectF GetItemRect(UIControl* item) const;

        /// 清空所有项目
        void ClearItems();

    private:
        struct FlexItem
        {
            UIControl* control;
            FlexItemProperties properties;
            ExRectF calculated_rect;
            float main_size;
            float cross_size;
            bool is_frozen;
        };

        struct FlexLine
        {
            std::vector<FlexItem*> items;
            float main_size;
            float cross_size;
            float remaining_space;
        };

        FlexContainerProperties m_container_properties;
        std::vector<FlexItem> m_items;
        std::unordered_map<UIControl*, size_t> m_item_indices;

        void SortItemsByOrder();
        std::vector<FlexLine> CreateFlexLines(const ExRectF& container_rect);
        void ResolveFlexibleLengths(FlexLine& line, float available_space);
        void AlignItems(const std::vector<FlexLine>& lines, const ExRectF& container_rect);
        void AlignContent(const std::vector<FlexLine>& lines, const ExRectF& container_rect);
        
        float GetMainAxisSize(const ExRectF& rect) const;
        float GetCrossAxisSize(const ExRectF& rect) const;
        void SetMainAxisSize(ExRectF& rect, float size) const;
        void SetCrossAxisSize(ExRectF& rect, float size) const;
        float GetMainAxisPosition(const ExRectF& rect) const;
        float GetCrossAxisPosition(const ExRectF& rect) const;
        void SetMainAxisPosition(ExRectF& rect, float position) const;
        void SetCrossAxisPosition(ExRectF& rect, float position) const;
    };

    /// 响应式布局管理器
    class UIResponsiveLayoutManager
    {
    public:
        UIResponsiveLayoutManager();
        ~UIResponsiveLayoutManager();

        /// 设置断点
        void SetBreakpoints(const std::unordered_map<ResponsiveBreakpoint, float>& breakpoints);

        /// 设置控件响应式属性
        void SetResponsiveProperties(UIControl* control, const ResponsiveProperties& properties);

        /// 更新当前屏幕尺寸
        void UpdateScreenSize(float width, float height);

        /// 应用响应式布局
        void ApplyResponsiveLayout();

        /// 获取当前断点
        ResponsiveBreakpoint GetCurrentBreakpoint() const { return m_current_breakpoint; }

    private:
        std::unordered_map<ResponsiveBreakpoint, float> m_breakpoints;
        std::unordered_map<UIControl*, ResponsiveProperties> m_responsive_properties;
        
        float m_screen_width;
        float m_screen_height;
        ResponsiveBreakpoint m_current_breakpoint;

        ResponsiveBreakpoint CalculateBreakpoint(float width) const;
        void ApplyPropertiesForBreakpoint(UIControl* control, ResponsiveBreakpoint breakpoint);
    };

    /// 约束布局引擎
    class UIConstraintLayoutEngine
    {
    public:
        UIConstraintLayoutEngine();
        ~UIConstraintLayoutEngine();

        /// 添加约束
        void AddConstraint(const LayoutConstraint& constraint);

        /// 移除约束
        void RemoveConstraint(size_t constraint_id);

        /// 移除控件的所有约束
        void RemoveConstraintsForControl(UIControl* control);

        /// 计算布局
        bool SolveConstraints(const ExRectF& container_rect);

        /// 获取控件计算后的矩形
        ExRectF GetControlRect(UIControl* control) const;

        /// 清空所有约束
        void ClearConstraints();

        /// 验证约束系统
        bool ValidateConstraints() const;

    private:
        struct ConstraintVariable
        {
            UIControl* control;
            ConstraintAttribute attribute;
            float value;
            bool is_constant;
        };

        std::vector<LayoutConstraint> m_constraints;
        std::unordered_map<UIControl*, ExRectF> m_control_rects;
        std::vector<ConstraintVariable> m_variables;

        bool SolveLinearSystem();
        float GetAttributeValue(UIControl* control, ConstraintAttribute attribute) const;
        void SetAttributeValue(UIControl* control, ConstraintAttribute attribute, float value);
        bool HasCircularDependency() const;
    };

    /// 增强布局管理器
    class UIEnhancedLayoutManager
    {
    public:
        UIEnhancedLayoutManager();
        ~UIEnhancedLayoutManager();

        /// 初始化布局管理器
        HRESULT Initialize();

        /// 关闭布局管理器
        void Shutdown();

        /// 创建Flexbox布局
        ExSharedPtr<UIFlexboxLayoutEngine> CreateFlexboxLayout();

        /// 创建响应式布局
        ExSharedPtr<UIResponsiveLayoutManager> CreateResponsiveLayout();

        /// 创建约束布局
        ExSharedPtr<UIConstraintLayoutEngine> CreateConstraintLayout();

        /// 注册布局容器
        void RegisterLayoutContainer(UIControl* container, ExSharedPtr<UIFlexboxLayoutEngine> layout);

        /// 注销布局容器
        void UnregisterLayoutContainer(UIControl* container);

        /// 更新所有布局
        void UpdateAllLayouts();

        /// 设置自动布局更新
        void SetAutoUpdate(bool enable) { m_auto_update = enable; }

        /// 获取统计信息
        struct LayoutStats
        {
            uint32_t flexbox_layouts;
            uint32_t responsive_layouts;
            uint32_t constraint_layouts;
            float total_calculation_time_ms;
            uint32_t layout_updates_per_frame;
        };
        const LayoutStats& GetStats() const { return m_stats; }

    private:
        std::unordered_map<UIControl*, ExSharedPtr<UIFlexboxLayoutEngine>> m_flexbox_layouts;
        std::vector<ExSharedPtr<UIResponsiveLayoutManager>> m_responsive_layouts;
        std::vector<ExSharedPtr<UIConstraintLayoutEngine>> m_constraint_layouts;
        
        bool m_auto_update;
        LayoutStats m_stats;

        void CalculateLayoutPerformance();
    };

    /// 全局增强布局管理器实例
    extern UIEnhancedLayoutManager* g_enhanced_layout_manager;

    /// 便捷宏定义
    #define LAYOUT_CREATE_FLEXBOX() if(g_enhanced_layout_manager) g_enhanced_layout_manager->CreateFlexboxLayout()
    #define LAYOUT_CREATE_RESPONSIVE() if(g_enhanced_layout_manager) g_enhanced_layout_manager->CreateResponsiveLayout()
    #define LAYOUT_CREATE_CONSTRAINT() if(g_enhanced_layout_manager) g_enhanced_layout_manager->CreateConstraintLayout()
    #define LAYOUT_UPDATE_ALL() if(g_enhanced_layout_manager) g_enhanced_layout_manager->UpdateAllLayouts()
}
