/**
** =====================================================================================
**
**       文件名称: enhanced_animation.cpp
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】增强动画系统 - 现代化动画引擎与插值框架 （实现文件）
**
** =====================================================================================
**/

#include "pch.h"
#include "enhanced_animation.h"
#include <cmath>

namespace HHBUI
{
    // 全局增强动画管理器实例
    UIEnhancedAnimationManager* g_enhanced_animation_manager = nullptr;

    // 模板特化：浮点数插值
    template<>
    float UIAnimationTrack<float>::Interpolate(const float& a, const float& b, float t) const
    {
        return a + (b - a) * t;
    }

    // 模板特化：DirectX::XMFLOAT2插值
    template<>
    DirectX::XMFLOAT2 UIAnimationTrack<DirectX::XMFLOAT2>::Interpolate(const DirectX::XMFLOAT2& a, const DirectX::XMFLOAT2& b, float t) const
    {
        return DirectX::XMFLOAT2(
            a.x + (b.x - a.x) * t,
            a.y + (b.y - a.y) * t
        );
    }

    // 模板特化：DirectX::XMFLOAT3插值
    template<>
    DirectX::XMFLOAT3 UIAnimationTrack<DirectX::XMFLOAT3>::Interpolate(const DirectX::XMFLOAT3& a, const DirectX::XMFLOAT3& b, float t) const
    {
        return DirectX::XMFLOAT3(
            a.x + (b.x - a.x) * t,
            a.y + (b.y - a.y) * t,
            a.z + (b.z - a.z) * t
        );
    }

    // 模板特化：DirectX::XMFLOAT4插值
    template<>
    DirectX::XMFLOAT4 UIAnimationTrack<DirectX::XMFLOAT4>::Interpolate(const DirectX::XMFLOAT4& a, const DirectX::XMFLOAT4& b, float t) const
    {
        return DirectX::XMFLOAT4(
            a.x + (b.x - a.x) * t,
            a.y + (b.y - a.y) * t,
            a.z + (b.z - a.z) * t,
            a.w + (b.w - a.w) * t
        );
    }

    // 模板特化：缓动函数应用
    template<>
    float UIAnimationTrack<float>::ApplyEasing(float t, EasingType easing) const
    {
        return UIEasingFunctions::ApplyEasing(t, easing);
    }

    template<>
    float UIAnimationTrack<DirectX::XMFLOAT2>::ApplyEasing(float t, EasingType easing) const
    {
        return UIEasingFunctions::ApplyEasing(t, easing);
    }

    template<>
    float UIAnimationTrack<DirectX::XMFLOAT3>::ApplyEasing(float t, EasingType easing) const
    {
        return UIEasingFunctions::ApplyEasing(t, easing);
    }

    template<>
    float UIAnimationTrack<DirectX::XMFLOAT4>::ApplyEasing(float t, EasingType easing) const
    {
        return UIEasingFunctions::ApplyEasing(t, easing);
    }

    // UIAnimationClip 实现
    UIAnimationClip::UIAnimationClip()
        : m_duration(1.0f)
        , m_loop_mode(AnimationLoopMode::ONCE)
    {
    }

    UIAnimationClip::~UIAnimationClip()
    {
    }

    void UIAnimationClip::AddFloatTrack(const std::string& property, const UIAnimationTrack<float>& track)
    {
        m_float_tracks[property] = track;
    }

    void UIAnimationClip::AddVector2Track(const std::string& property, const UIAnimationTrack<DirectX::XMFLOAT2>& track)
    {
        m_vector2_tracks[property] = track;
    }

    void UIAnimationClip::AddVector3Track(const std::string& property, const UIAnimationTrack<DirectX::XMFLOAT3>& track)
    {
        m_vector3_tracks[property] = track;
    }

    void UIAnimationClip::AddVector4Track(const std::string& property, const UIAnimationTrack<DirectX::XMFLOAT4>& track)
    {
        m_vector4_tracks[property] = track;
    }

    void UIAnimationClip::AddColorTrack(const std::string& property, const UIAnimationTrack<DirectX::XMFLOAT4>& track)
    {
        m_color_tracks[property] = track;
    }

    bool UIAnimationClip::GetFloatValue(const std::string& property, float time, float& value) const
    {
        auto it = m_float_tracks.find(property);
        if (it != m_float_tracks.end())
        {
            value = it->second.GetInterpolatedValue(time);
            return true;
        }
        return false;
    }

    bool UIAnimationClip::GetVector2Value(const std::string& property, float time, DirectX::XMFLOAT2& value) const
    {
        auto it = m_vector2_tracks.find(property);
        if (it != m_vector2_tracks.end())
        {
            value = it->second.GetInterpolatedValue(time);
            return true;
        }
        return false;
    }

    bool UIAnimationClip::GetVector3Value(const std::string& property, float time, DirectX::XMFLOAT3& value) const
    {
        auto it = m_vector3_tracks.find(property);
        if (it != m_vector3_tracks.end())
        {
            value = it->second.GetInterpolatedValue(time);
            return true;
        }
        return false;
    }

    bool UIAnimationClip::GetVector4Value(const std::string& property, float time, DirectX::XMFLOAT4& value) const
    {
        auto it = m_vector4_tracks.find(property);
        if (it != m_vector4_tracks.end())
        {
            value = it->second.GetInterpolatedValue(time);
            return true;
        }
        return false;
    }

    bool UIAnimationClip::GetColorValue(const std::string& property, float time, DirectX::XMFLOAT4& value) const
    {
        auto it = m_color_tracks.find(property);
        if (it != m_color_tracks.end())
        {
            value = it->second.GetInterpolatedValue(time);
            return true;
        }
        return false;
    }

    // UIAnimationInstance 实现
    UIAnimationInstance::UIAnimationInstance()
        : m_state(AnimationState::STOPPED)
        , m_current_time(0.0f)
        , m_speed(1.0f)
    {
    }

    UIAnimationInstance::~UIAnimationInstance()
    {
    }

    void UIAnimationInstance::SetClip(ExSharedPtr<UIAnimationClip> clip)
    {
        m_clip = clip;
        m_current_time = 0.0f;
        m_state = AnimationState::STOPPED;
    }

    void UIAnimationInstance::Play()
    {
        if (m_clip)
        {
            m_state = AnimationState::PLAYING;
        }
    }

    void UIAnimationInstance::Pause()
    {
        if (m_state == AnimationState::PLAYING)
        {
            m_state = AnimationState::PAUSED;
        }
    }

    void UIAnimationInstance::Stop()
    {
        m_state = AnimationState::STOPPED;
        m_current_time = 0.0f;
    }

    void UIAnimationInstance::SetTime(float time)
    {
        if (m_clip)
        {
            m_current_time = std::max(0.0f, std::min(time, m_clip->GetDuration()));
        }
    }

    float UIAnimationInstance::GetNormalizedTime() const
    {
        return CalculateNormalizedTime();
    }

    void UIAnimationInstance::Update(float delta_time)
    {
        if (!m_clip || m_state != AnimationState::PLAYING)
            return;

        m_current_time += delta_time * m_speed;

        float duration = m_clip->GetDuration();
        if (m_current_time >= duration)
        {
            switch (m_clip->GetLoopMode())
            {
            case AnimationLoopMode::ONCE:
                m_current_time = duration;
                m_state = AnimationState::COMPLETED;
                CheckCompletion();
                break;

            case AnimationLoopMode::LOOP:
                m_current_time = fmod(m_current_time, duration);
                break;

            case AnimationLoopMode::PING_PONG:
                // 简化的乒乓实现
                m_current_time = duration - fmod(m_current_time - duration, duration);
                break;

            case AnimationLoopMode::CLAMP:
                m_current_time = duration;
                break;
            }
        }
    }

    bool UIAnimationInstance::GetFloatValue(const std::string& property, float& value) const
    {
        if (!m_clip)
            return false;

        float normalized_time = CalculateNormalizedTime();
        return m_clip->GetFloatValue(property, normalized_time, value);
    }

    bool UIAnimationInstance::GetVector2Value(const std::string& property, DirectX::XMFLOAT2& value) const
    {
        if (!m_clip)
            return false;

        float normalized_time = CalculateNormalizedTime();
        return m_clip->GetVector2Value(property, normalized_time, value);
    }

    bool UIAnimationInstance::GetVector3Value(const std::string& property, DirectX::XMFLOAT3& value) const
    {
        if (!m_clip)
            return false;

        float normalized_time = CalculateNormalizedTime();
        return m_clip->GetVector3Value(property, normalized_time, value);
    }

    bool UIAnimationInstance::GetVector4Value(const std::string& property, DirectX::XMFLOAT4& value) const
    {
        if (!m_clip)
            return false;

        float normalized_time = CalculateNormalizedTime();
        return m_clip->GetVector4Value(property, normalized_time, value);
    }

    bool UIAnimationInstance::GetColorValue(const std::string& property, DirectX::XMFLOAT4& value) const
    {
        if (!m_clip)
            return false;

        float normalized_time = CalculateNormalizedTime();
        return m_clip->GetColorValue(property, normalized_time, value);
    }

    float UIAnimationInstance::CalculateNormalizedTime() const
    {
        if (!m_clip || m_clip->GetDuration() <= 0.0f)
            return 0.0f;

        return m_current_time / m_clip->GetDuration();
    }

    void UIAnimationInstance::CheckCompletion()
    {
        if (m_state == AnimationState::COMPLETED && m_completion_callback)
        {
            m_completion_callback();
        }
    }

    // UIEasingFunctions 实现
    float UIEasingFunctions::ApplyEasing(float t, EasingType type)
    {
        switch (type)
        {
        case EasingType::LINEAR: return Linear(t);
        case EasingType::EASE_IN_QUAD: return EaseInQuad(t);
        case EasingType::EASE_OUT_QUAD: return EaseOutQuad(t);
        case EasingType::EASE_IN_OUT_QUAD: return EaseInOutQuad(t);
        case EasingType::EASE_IN_CUBIC: return EaseInCubic(t);
        case EasingType::EASE_OUT_CUBIC: return EaseOutCubic(t);
        case EasingType::EASE_IN_OUT_CUBIC: return EaseInOutCubic(t);
        case EasingType::EASE_IN_SINE: return EaseInSine(t);
        case EasingType::EASE_OUT_SINE: return EaseOutSine(t);
        case EasingType::EASE_IN_OUT_SINE: return EaseInOutSine(t);
        default: return Linear(t);
        }
    }

    float UIEasingFunctions::Linear(float t)
    {
        return t;
    }

    float UIEasingFunctions::EaseInQuad(float t)
    {
        return t * t;
    }

    float UIEasingFunctions::EaseOutQuad(float t)
    {
        return 1.0f - (1.0f - t) * (1.0f - t);
    }

    float UIEasingFunctions::EaseInOutQuad(float t)
    {
        return t < 0.5f ? 2.0f * t * t : 1.0f - pow(-2.0f * t + 2.0f, 2.0f) / 2.0f;
    }

    float UIEasingFunctions::EaseInCubic(float t)
    {
        return t * t * t;
    }

    float UIEasingFunctions::EaseOutCubic(float t)
    {
        return 1.0f - pow(1.0f - t, 3.0f);
    }

    float UIEasingFunctions::EaseInOutCubic(float t)
    {
        return t < 0.5f ? 4.0f * t * t * t : 1.0f - pow(-2.0f * t + 2.0f, 3.0f) / 2.0f;
    }

    float UIEasingFunctions::EaseInSine(float t)
    {
        return 1.0f - cos((t * 3.14159f) / 2.0f);
    }

    float UIEasingFunctions::EaseOutSine(float t)
    {
        return sin((t * 3.14159f) / 2.0f);
    }

    float UIEasingFunctions::EaseInOutSine(float t)
    {
        return -(cos(3.14159f * t) - 1.0f) / 2.0f;
    }

    // 其他缓动函数的实现可以继续添加...
}
