/**
** =====================================================================================
**
**       文件名称: enhanced_engine_integration.h
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】增强引擎集成系统 - 统一的增强功能初始化与管理框架 （声明文件）
**
**       主要功能:
**       - 统一的增强系统初始化
**       - 系统间依赖关系管理
**       - 配置参数统一管理
**       - 性能监控集成
**       - 错误处理与恢复
**       - 系统状态监控
**       - 资源清理与释放
**
**       技术特性:
**       - 采用现代C++17标准与RAII管理
**       - 智能指针与自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 模块化系统架构
**       - 配置驱动的初始化
**       - 实时状态监控
**       - 优雅的系统关闭
**
**       更新记录:
**       2025-07-31 v1.0.0.0 : 1. 创建增强引擎集成系统
**                             2. 实现统一初始化框架
**                             3. 添加依赖关系管理
**                             4. 完成配置参数管理
**                             5. 集成性能监控
**                             6. 实现错误处理机制
**                             7. 确保资源清理
**
** =====================================================================================
**/

#pragma once
#include "render_api.h"
#include "common/smart_ptr.hpp"
#include <unordered_map>
#include <vector>
#include <string>
#include <functional>
#include <chrono>
#include <mutex>
#include <future>

namespace HHBUI
{
    /// 增强系统类型
    enum class EnhancedSystemType : uint32_t
    {
        RENDER_BATCH = 0,           // 渲染批处理系统
        TEXTURE_MANAGER,            // 纹理管理系统
        COMMAND_QUEUE,              // 命令队列系统
        ANTIALIASING,               // 抗锯齿系统
        POST_PROCESSING,            // 后处理系统
        RENDER_PIPELINE,            // 渲染管线系统
        RESOURCE_STREAMING,         // 资源流式系统
        ENHANCED_ANIMATION,         // 增强动画系统
        ENHANCED_LAYOUT,            // 增强布局系统
        PERFORMANCE_MONITOR         // 性能监控系统
    };

    /// 系统状态
    enum class SystemState : uint32_t
    {
        UNINITIALIZED = 0,
        INITIALIZING,
        INITIALIZED,
        SYSTEM_ERROR,
        SHUTTING_DOWN,
        SHUTDOWN
    };

    /// 系统信息结构体
    struct SystemInfo
    {
        std::wstring name;                      // 系统名称
        std::wstring description;               // 系统描述
        std::wstring version;                   // 系统版本
        SystemState state = SystemState::UNINITIALIZED;  // 系统状态
        std::function<HRESULT()> init_func;     // 初始化函数
        std::function<void()> shutdown_func;    // 关闭函数
        std::function<void()> update_func;      // 更新函数
        std::vector<EnhancedSystemType> dependencies;  // 依赖系统
        uint32_t priority = 0;                  // 优先级
        bool is_critical = false;               // 是否为关键系统
        std::chrono::steady_clock::time_point last_update;  // 最后更新时间
        uint64_t update_count = 0;              // 更新次数
        std::chrono::milliseconds avg_update_time{0};  // 平均更新时间
    };

    /// 增强引擎配置
    struct EnhancedEngineConfig
    {
        // 渲染批处理配置
        struct BatchConfig
        {
            bool enable_instancing = true;
            bool enable_sorting = true;
            uint32_t max_instances_per_batch = 1000;
            uint32_t dynamic_buffer_size = 1024 * 1024;
        } batch_config;

        // 纹理管理配置
        struct TextureConfig
        {
            uint32_t worker_thread_count = 2;
            uint64_t cache_memory_limit = 256 * 1024 * 1024;
            uint32_t cache_texture_limit = 1000;
            bool enable_compression = true;
        } texture_config;

        // 命令队列配置
        struct CommandQueueConfig
        {
            uint32_t max_commands = 10000;
            uint32_t worker_thread_count = 2;
            bool enable_sorting = true;
            uint32_t update_frequency = 60;
            uint32_t render_frequency = 60;
        } command_queue_config;

        // 抗锯齿配置
        struct AntiAliasingConfig
        {
            AntiAliasingType default_type = AntiAliasingType::FXAA;
            AntiAliasingQuality default_quality = AntiAliasingQuality::MEDIUM;
            bool enable_adaptive = true;
            float target_frametime_ms = 16.67f;
        } antialiasing_config;

        // 后处理配置
        struct PostProcessingConfig
        {
            bool enable_bloom = true;
            bool enable_hdr = true;
            bool enable_tone_mapping = true;
            PostProcessQuality default_quality = PostProcessQuality::MEDIUM;
        } post_processing_config;

        // 渲染管线配置
        struct PipelineConfig
        {
            RenderPipelineType default_type = RenderPipelineType::FORWARD;
            bool enable_deferred = true;
            bool enable_state_cache = true;
        } pipeline_config;

        // 资源流式配置
        struct StreamingConfig
        {
            uint32_t worker_thread_count = 4;
            uint64_t memory_budget = 512 * 1024 * 1024;
            bool enable_lod = true;
            bool enable_atlas = true;
        } streaming_config;

        // 动画配置
        struct AnimationConfig
        {
            bool enable_skeletal = true;
            bool enable_state_machine = true;
            float global_speed = 1.0f;
        } animation_config;

        // 布局配置
        struct LayoutConfig
        {
            bool enable_flexbox = true;
            bool enable_responsive = true;
            bool enable_constraints = true;
            bool auto_update = true;
        } layout_config;

        // 性能监控配置
        struct MonitorConfig
        {
            bool enable_monitoring = true;
            bool enable_auto_optimization = true;
            uint32_t history_size = 1000;
            float target_fps = 60.0f;
            size_t memory_budget = 1024 * 1024 * 1024;
        } monitor_config;

        EnhancedEngineConfig() = default;
    };

    /// 增强引擎集成管理器
    class UIEnhancedEngineIntegration
    {
    public:
        UIEnhancedEngineIntegration();
        ~UIEnhancedEngineIntegration();

        /// 初始化增强引擎
        HRESULT Initialize(ID3D11Device* device, ID3D11DeviceContext* context,
                          uint32_t width, uint32_t height,
                          const EnhancedEngineConfig& config = EnhancedEngineConfig{});

        /// 关闭增强引擎
        void Shutdown();

        /// 更新增强引擎
        void Update(float delta_time);

        /// 开始帧渲染
        void BeginFrame(uint32_t frame_id);

        /// 结束帧渲染
        void EndFrame();

        /// 获取系统状态
        SystemState GetSystemState(EnhancedSystemType system) const;

        /// 获取所有系统状态
        std::unordered_map<EnhancedSystemType, SystemState> GetAllSystemStates() const;

        /// 启用/禁用系统
        void SetSystemEnabled(EnhancedSystemType system, bool enabled);

        /// 检查系统是否启用
        bool IsSystemEnabled(EnhancedSystemType system) const;

        /// 重新配置系统
        HRESULT ReconfigureSystem(EnhancedSystemType system, const void* config);

        /// 获取系统错误信息
        std::string GetSystemError(EnhancedSystemType system) const;

        /// 获取集成统计信息
        struct IntegrationStats
        {
            uint32_t initialized_systems;
            uint32_t active_systems;
            uint32_t error_systems;
            float total_initialization_time_ms;
            float total_update_time_ms;
            std::unordered_map<EnhancedSystemType, float> system_update_times_ms;
        };
        const IntegrationStats& GetStats() const { return m_stats; }

        /// 生成系统报告
        std::string GenerateSystemReport() const;

        /// 导出配置
        HRESULT ExportConfiguration(LPCWSTR file_path) const;

        /// 导入配置
        HRESULT ImportConfiguration(LPCWSTR file_path);

    private:
        struct SystemInfo
        {
            SystemState state;
            bool enabled;
            std::string error_message;
            std::function<HRESULT()> init_func;
            std::function<void()> shutdown_func;
            std::function<void(float)> update_func;
            std::chrono::steady_clock::time_point last_update_time;
            float update_time_ms;
        };

        ID3D11Device* m_device;
        ID3D11DeviceContext* m_context;
        uint32_t m_width;
        uint32_t m_height;
        
        EnhancedEngineConfig m_config;
        std::unordered_map<EnhancedSystemType, SystemInfo> m_systems;
        IntegrationStats m_stats;

        bool m_initialized;
        std::chrono::steady_clock::time_point m_initialization_start_time;

        // 线程同步
        mutable std::mutex m_execution_mutex;
        mutable std::mutex m_systems_mutex;
        mutable std::mutex m_stats_mutex;

        // 系统初始化函数
        HRESULT InitializeRenderBatchSystem();
        HRESULT InitializeTextureManagerSystem();
        HRESULT InitializeCommandQueueSystem();
        HRESULT InitializeAntiAliasingSystem();
        HRESULT InitializePostProcessingSystem();
        HRESULT InitializeRenderPipelineSystem();
        HRESULT InitializeResourceStreamingSystem();
        HRESULT InitializeEnhancedAnimationSystem();
        HRESULT InitializeEnhancedLayoutSystem();
        HRESULT InitializePerformanceMonitorSystem();

        // 系统关闭函数
        void ShutdownRenderBatchSystem();
        void ShutdownTextureManagerSystem();
        void ShutdownCommandQueueSystem();
        void ShutdownAntiAliasingSystem();
        void ShutdownPostProcessingSystem();
        void ShutdownRenderPipelineSystem();
        void ShutdownResourceStreamingSystem();
        void ShutdownEnhancedAnimationSystem();
        void ShutdownEnhancedLayoutSystem();
        void ShutdownPerformanceMonitorSystem();

        // 系统更新函数
        void UpdateRenderBatchSystem(float delta_time);
        void UpdateTextureManagerSystem(float delta_time);
        void UpdateCommandQueueSystem(float delta_time);
        void UpdateAntiAliasingSystem(float delta_time);
        void UpdatePostProcessingSystem(float delta_time);
        void UpdateRenderPipelineSystem(float delta_time);
        void UpdateResourceStreamingSystem(float delta_time);
        void UpdateEnhancedAnimationSystem(float delta_time);
        void UpdateEnhancedLayoutSystem(float delta_time);
        void UpdatePerformanceMonitorSystem(float delta_time);

        // 内部方法
        void RegisterAllSystems();
        void RegisterSystem(EnhancedSystemType type, const SystemInfo& info);
        void UpdateSystemStats();
        std::vector<EnhancedSystemType> GetInitializationOrder() const;
        std::vector<EnhancedSystemType> GetShutdownOrder() const;
        bool CheckSystemDependencies(EnhancedSystemType system) const;
    };

    /// 全局增强引擎集成管理器实例
    extern UIEnhancedEngineIntegration* g_enhanced_engine_integration;

    /// 便捷宏定义
    #define ENHANCED_ENGINE_INIT(device, context, width, height, config) \
        if(g_enhanced_engine_integration) g_enhanced_engine_integration->Initialize(device, context, width, height, config)
    #define ENHANCED_ENGINE_UPDATE(delta) \
        if(g_enhanced_engine_integration) g_enhanced_engine_integration->Update(delta)
    #define ENHANCED_ENGINE_BEGIN_FRAME(id) \
        if(g_enhanced_engine_integration) g_enhanced_engine_integration->BeginFrame(id)
    #define ENHANCED_ENGINE_END_FRAME() \
        if(g_enhanced_engine_integration) g_enhanced_engine_integration->EndFrame()
    #define ENHANCED_ENGINE_SHUTDOWN() \
        if(g_enhanced_engine_integration) g_enhanced_engine_integration->Shutdown()

    /// RAII增强引擎帧管理类
    class ScopedEnhancedEngineFrame
    {
    public:
        ScopedEnhancedEngineFrame(uint32_t frame_id)
        {
            ENHANCED_ENGINE_BEGIN_FRAME(frame_id);
        }
        
        ~ScopedEnhancedEngineFrame()
        {
            ENHANCED_ENGINE_END_FRAME();
        }
    };

    #define ENHANCED_ENGINE_SCOPED_FRAME(id) ScopedEnhancedEngineFrame _scoped_enhanced_frame(id)
}
