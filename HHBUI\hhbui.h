﻿/*
** 版权所有 （c） 2025 HHBUI ：https://hhbui.com/
** 保留所有权利。
** 感谢 LIB：SOUI，EXDUI，ZLIB，PUGIXML...
**
** 本软件由版权所有者和贡献者“按原样”提供
** 以及任何明示或暗示的保证，包括但不限于
** 对适销性和特定用途适用性的默示保证
** 被否认。在任何情况下，版权所有者或贡献者均不得
** 对任何直接、间接、偶然、特殊、惩戒性或
** 间接损害赔偿（包括但不限于采购
** 替代商品或服务;使用、数据或利润损失;或 BUSINESS
** 中断）无论造成何种原因和任何责任理论，无论是在
** 合同、严格责任或侵权行为（包括疏忽或其他）
** 因使用本软件而引起的任何原因，即使已告知
** 此类损坏的可能性。
*/

#pragma once

/*
** HHBUI 主头文件 - 完整功能包含
**
** 使用建议：
** - 对于只需要核心功能的项目，请使用 hhbui_core.h
** - 对于只需要UI组件的项目，请使用 hhbui_ui.h
** - 对于只需要控件的项目，请使用 hhbui_controls.h
** - 对于只需要渲染功能的项目，请使用 hhbui_render.h
** - 只有需要完整功能时才包含此文件
*/

// 包含所有模块
#include "hhbui_core.h"
#include "hhbui_ui.h"
#include "hhbui_controls.h"
#include "hhbui_render.h"

// 其他工具类
#include "common/ziparchive.h"
#include "common/enhanced_mem_pool.hpp"
#include "common/enhanced_exception.hpp"
#include "common/resource_manager.hpp"


