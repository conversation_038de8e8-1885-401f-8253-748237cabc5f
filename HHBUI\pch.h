﻿#pragma once

// Windows 头文件
#define WIN32_LEAN_AND_MEAN
#include <windows.h>

// DirectX 头文件
#include <d3d11.h>
#include <d2d1_1.h>
#include <dwrite.h>
#include <DirectXMath.h>
#include <wrl.h>

// 标准库头文件
#include <memory>
#include <vector>
#include <unordered_map>
#include <string>
#include <functional>
#include <chrono>
#include <mutex>
#include <atomic>
#include <thread>
#include <future>
#include <algorithm>
#include <cfloat>

#include "hhbui.h"
