﻿#pragma once

// Windows 头文件 - 仅包含最常用的
#define WIN32_LEAN_AND_MEAN
#define NOMINMAX
#include <windows.h>
#include <windowsx.h>

// DirectX 头文件 - 按使用频率排序
#include <d3d11.h>
#include <d2d1_1.h>
#include <dwrite.h>
#include <DirectXMath.h>
#include <wrl.h>

// 标准库头文件 - 仅包含最常用的
#include <memory>
#include <vector>
#include <unordered_map>
#include <string>
#include <functional>
#include <chrono>
#include <mutex>
#include <atomic>
#include <thread>
#include <algorithm>

// 基础类型定义
#include <inttypes.h>
#include <tchar.h>
#include <stdarg.h>
#include <stdint.h>
#include <limits.h>

// 移除主头文件包含，改为包含核心定义
#include "application/define.h"
