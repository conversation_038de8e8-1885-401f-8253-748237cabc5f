﻿#pragma once
typedef unsigned __int64 QWORD;
typedef DWORD HSTREAM;		// sample stream handle
typedef DWORD HPLUGIN;		// plugin handle

namespace HHBUI
{
	enum info_wavering_type {
        DyCircle,       //动感の圆
        Ripple,         //波纹
        RectangleFFT,   //矩形频谱
	};
	class TOAPI UIWaveRingView : public UIControl
	{
	public:
		UIWaveRingView(UIBase *hParent, INT x = 0, INT y = 0, INT width = 0, INT height = 0, LPCWSTR lpszName = 0, INT dwStyle = 0, INT dwStyleEx = 0, INT nID = 0, INT dwTextFormat = -1);
        //设置类型
        void SetType(info_wavering_type type);
        //设置是否旋转
        void SetOrRotate(BOOL rotate);
        //设置否随机角度
        void SetOrRandom(BOOL random);
        //设置是否绘制基线
        void SetOrDrawbase(BOOL drawbase);
        //设置是否绘制波纹
        void SetOrDrawWave(BOOL drawwave);
        //置是否绘制能量点
        void SetOrDrawPoint(BOOL drawpoint);
        //设置是否均衡能量强度
        void SetOrPoweroffSet(BOOL poweroffset);
        //设置能量点是否扩散
        void SetOrSpread(BOOL spread);
        //设置是否绘制文本
        void SetOrDrawText(BOOL drawtext);
        //设置文本、头像是否抖动
        void SetTextOfMove(BOOL textmove);
        //设置是否填充
        void SetOrfill(BOOL isfill);
        //设置特效是否开启Bloom
        void SetFxORbloom(BOOL fxORbloom);
        //设置是否绘制特效
        void SetOrDrawFx(BOOL drawfx);
        //设置频谱颜色 为空默认七彩色
        void SetOrColor(UIColor Orcolor);
        //设置特效颜色
        void SetFxColor(UIColor fxcolor);
        void SetMaximumlight(float voligh);
        //设置能量阈值
        void SetScope(INT scope);
        //置能量点与圆环距离
        void SetDistance(INT distance);
        //设置能量点扩散速度 1-20
        void SetSpeed(INT speed);
        //设置专辑图像大小
        void SetAlbumSize(INT fscaleSize);
        //设置专辑图像 必须先设置专辑图像大小
        void SetAlbum(UIImage* dstImgw);

        /*BASS 播放功能相关*/
        /*
        * 设置bass动态库路径
        * fInitb：是否从内部初始化 此时支持SetPlayFile
        */
        HMODULE SetBassDll(LPCWSTR libPath, BOOL fInitb = false);
        //设置动态库句柄 [如何设置了路径就不需要设置句柄]
        void SetBassDllHandle(HMODULE handle);
        /*设置FXdll路径*/
        HMODULE SetBassFXDll(LPCWSTR libPath);
        //设置播放文件路径,需要SetBassDll设置fInitb
        HSTREAM SetBassPlayFile(LPCWSTR playfile);
        //设置播放文件句柄
        void SetBassHandle(HSTREAM handle, DWORD bpmChan = 0);
        //取播放位置 最大位置默认1000
        double GetBassCurPosition();
        //置播放位置
        void SetBassCurPosition(int position);
        //取播放总时间
        double GetBassPlayTotalTime();
        //取播放时间
        double GetBassPlayTime();
        //设置音量
        void SetBassVolume(float volume);
        //设置音量平衡
        void SetBassVolPan(float volpan);
        //设置混音增益
        void SetBassFx(float fInGain);
        /*设置节拍*/
        void SetBpm(float fBpm);
        /*获取节拍*/
        float GetBpm();

	protected:
		EXMETHOD LRESULT OnMsgProc(HWND hWnd, INT uMsg, WPARAM wParam, LPARAM lParam) override;
        EXMETHOD void OnPaintProc(ps_context ps) override;
        void createScope(UIColor pColor, float heightPercent, float leftDistance, float rightDistance);
        ExPointF calcPoint(int centerX, int centerY, int radius, float angle, ExPointF& point);
        void resetColor();
        float getRandomAngle();
        void initColor();
        void setWaveData(float* data);
		void waveringview_paint(float* data);
        void Rectangle_fft(float* data, ps_context ps);
        void Rectangle_DrawSpectrum(ps_context ps, float* data_spetral, float* data_peak, ExRectF rect, int col_width, int gap_width,
            int cols, bool draw_reflex = false, bool low_freq_in_center = false);
        void ripple_paint(float* data, ps_context ps);
        int DPIRound(double pixel, double round = 0.5);		//对结果进行四舍五入处理

        struct WaveViewBean
        {
            ExPointF point;
            ExPointF inner;//innerPoints
            ExPointF center;
            ExPointF outter;//outterPoints
            float angle;
            float radius;
            float powerPercent;
            UIColor pColor;
        };
		struct waveringview_s
		{
            UIColor fxcr = {};
            int Randomvalue = 0, hImgscale = 0, nRotatevalue = 0;
            ExPointF point = {};
            ExPointF centerPoint = {};
            FLOAT nTextWidth = 0.f, nTextHeight = 0.f, degress = 0.f, powerPercent = 0.f,
                angle = 0.f, radius = 0.f, powerPosition = 0.f, pMaximumlight = 0.5f, lowbpm = 0.0f;
            info_wavering_type type = DyCircle;
            bool isRotate = false;//是否旋转
            bool isRandom = false;//是否随机角度
            bool isBase = false;//是否绘制基线
            bool isWave = false;//是否绘制波纹
            bool isPoint = false;//是否绘制能量点
            bool isPowerOffset = false;//是否均衡能量强度
            bool isSpread = false;//能量点扩散
            bool isDrawText = false;//是否绘制文字
            bool isMove = false;//文字位移
            bool isfx = false;
            bool isfill = false;//填充
            bool isfxBloom = false;
            int scope = 0;//能量阈值
            int distance = 0;//能量点与圆环距离
            int speed = 0;//能量点扩散速度
            QWORD GetLength = 0;
            std::vector<WaveViewBean> list = {};
            std::vector<float> lastRadius = {};
            HSTREAM hBass = 0;
            UIImage* dstImgwhee = nullptr;
            UIBrush* ColorBrush = nullptr, * SolidBrush = nullptr, * hImgBrush = nullptr;
            UICanvas *hCanvas = nullptr;
            UICanvas *hMigCanvas = nullptr;
            HMODULE hModule = 0, hFxMod = 0;
		}p_data;

	};
}
