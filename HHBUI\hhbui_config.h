/*
** 版权所有 （c） 2025 HHBUI ：https://hhbui.com/
** 保留所有权利.
** HHBUI 编译配置头文件 - 优化编译性能
*/

#pragma once

// 编译优化配置
#ifndef HHBUI_COMPILE_CONFIG_H
#define HHBUI_COMPILE_CONFIG_H

// 模块开关 - 可以选择性编译需要的模块
#ifndef HHBUI_ENABLE_CORE
#define HHBUI_ENABLE_CORE 1
#endif

#ifndef HHBUI_ENABLE_UI
#define HHBUI_ENABLE_UI 1
#endif

#ifndef HHBUI_ENABLE_CONTROLS
#define HHBUI_ENABLE_CONTROLS 1
#endif

#ifndef HHBUI_ENABLE_RENDER
#define HHBUI_ENABLE_RENDER 1
#endif

#ifndef HHBUI_ENABLE_ENHANCED_RENDER
#define HHBUI_ENABLE_ENHANCED_RENDER 1
#endif

#ifndef HHBUI_ENABLE_ANIMATION
#define HHBUI_ENABLE_ANIMATION 1
#endif

#ifndef HHBUI_ENABLE_THIRD_PARTY
#define HHBUI_ENABLE_THIRD_PARTY 1
#endif

// 性能优化配置
#ifndef HHBUI_ENABLE_PERFORMANCE_MONITOR
#define HHBUI_ENABLE_PERFORMANCE_MONITOR 1
#endif

#ifndef HHBUI_ENABLE_MEMORY_POOL
#define HHBUI_ENABLE_MEMORY_POOL 1
#endif

// 调试配置
#ifndef HHBUI_ENABLE_DEBUG_INFO
#ifdef _DEBUG
#define HHBUI_ENABLE_DEBUG_INFO 1
#else
#define HHBUI_ENABLE_DEBUG_INFO 0
#endif
#endif

// 编译器优化提示
#ifdef _MSC_VER
    // 禁用不必要的警告
    #pragma warning(push)
    #pragma warning(disable: 4244)  // 数据类型转换警告
    #pragma warning(disable: 4996)  // 不安全函数警告
    
    // 启用内联优化
    #pragma inline_depth(255)
    #pragma inline_recursion(on)
#endif

// 平台特定配置
#ifdef _WIN32
    #ifndef WIN32_LEAN_AND_MEAN
    #define WIN32_LEAN_AND_MEAN
    #endif
    
    #ifndef NOMINMAX
    #define NOMINMAX
    #endif
#endif

#endif // HHBUI_COMPILE_CONFIG_H
