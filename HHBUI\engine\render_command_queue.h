/**
** =====================================================================================
**
**       文件名称: render_command_queue.h
**       创建时间: 2025-07-31
**       文件描述: 【HHBUI】多线程渲染命令队列系统 - 高性能渲染命令管理框架 （声明文件）
**
**       主要功能:
**       - 多线程安全的渲染命令队列
**       - 渲染命令自动排序与优化
**       - 后台资源加载线程管理
**       - UI更新与渲染分离机制
**       - 渲染命令批处理与合并
**       - 异步渲染任务调度
**       - 渲染线程同步与协调
**
**       技术特性:
**       - 采用现代C++17标准与多线程技术
**       - 无锁数据结构与原子操作
**       - 智能指针与RAII自动资源管理
**       - 异常安全保证与错误恢复机制
**       - 高性能命令队列算法
**       - 自适应线程池管理
**       - 实时性能监控与统计分析
**
**       更新记录:
**       2025-07-31 v1.0.0.0 : 1. 创建多线程渲染命令队列系统
**                             2. 实现渲染命令管理
**                             3. 添加后台资源加载支持
**                             4. 完成UI更新渲染分离
**                             5. 集成命令批处理优化
**                             6. 实现异步任务调度
**                             7. 确保线程安全性
**
** =====================================================================================
**/

#pragma once
#include "render_api.h"
#include "common/smart_ptr.hpp"
#include "common/enhanced_mem_pool.hpp"
#include <vector>
#include <queue>
#include <mutex>
#include <atomic>
#include <thread>
#include <condition_variable>
#include <functional>
#include <future>
#include <chrono>

namespace HHBUI
{
    /// 渲染命令类型
    enum class RenderCommandType : uint32_t
    {
        DRAW_INDEXED = 0,           // 索引绘制
        DRAW,                       // 顶点绘制
        DRAW_INSTANCED,             // 实例化绘制
        DRAW_INDEXED_INSTANCED,     // 索引实例化绘制
        SET_RENDER_TARGET,          // 设置渲染目标
        CLEAR_RENDER_TARGET,        // 清空渲染目标
        SET_VIEWPORT,               // 设置视口
        SET_SHADER,                 // 设置着色器
        SET_TEXTURE,                // 设置纹理
        SET_BUFFER,                 // 设置缓冲区
        SET_RENDER_STATE,           // 设置渲染状态
        PRESENT,                    // 呈现
        CUSTOM                      // 自定义命令
    };

    /// 渲染命令优先级
    enum class RenderCommandPriority : uint32_t
    {
        LOW = 0,
        NORMAL,
        HIGH,
        CRITICAL
    };

    /// 基础渲染命令
    struct RenderCommand
    {
        RenderCommandType type;
        RenderCommandPriority priority;
        uint64_t sort_key;
        uint32_t frame_id;
        std::function<void()> execute_func;
        
        RenderCommand() : type(RenderCommandType::CUSTOM), 
                         priority(RenderCommandPriority::NORMAL),
                         sort_key(0), frame_id(0) {}
        
        virtual ~RenderCommand() = default;
        virtual void Execute() { if (execute_func) execute_func(); }
    };

    /// 绘制命令
    struct DrawCommand : public RenderCommand
    {
        uint32_t vertex_count;
        uint32_t start_vertex;
        uint32_t instance_count;
        uint32_t start_instance;
        
        DrawCommand() : vertex_count(0), start_vertex(0), 
                       instance_count(1), start_instance(0)
        {
            type = RenderCommandType::DRAW;
        }
    };

    /// 索引绘制命令
    struct DrawIndexedCommand : public RenderCommand
    {
        uint32_t index_count;
        uint32_t start_index;
        int32_t base_vertex;
        uint32_t instance_count;
        uint32_t start_instance;
        
        DrawIndexedCommand() : index_count(0), start_index(0), base_vertex(0),
                              instance_count(1), start_instance(0)
        {
            type = RenderCommandType::DRAW_INDEXED;
        }
    };

    /// 渲染状态命令
    struct SetRenderStateCommand : public RenderCommand
    {
        ExSharedPtr<IRenderState> render_state;
        
        SetRenderStateCommand() 
        {
            type = RenderCommandType::SET_RENDER_STATE;
        }
    };

    /// 着色器命令
    struct SetShaderCommand : public RenderCommand
    {
        ExSharedPtr<IShader> vertex_shader;
        ExSharedPtr<IShader> pixel_shader;
        ExSharedPtr<IShader> geometry_shader;
        ExSharedPtr<IShader> hull_shader;
        ExSharedPtr<IShader> domain_shader;
        ExSharedPtr<IShader> compute_shader;
        
        SetShaderCommand()
        {
            type = RenderCommandType::SET_SHADER;
        }
    };

    /// 渲染命令队列
    class UIRenderCommandQueue
    {
    public:
        UIRenderCommandQueue();
        ~UIRenderCommandQueue();

        /// 初始化命令队列
        HRESULT Initialize(uint32_t max_commands = 10000);

        /// 关闭命令队列
        void Shutdown();

        /// 开始新帧
        void BeginFrame(uint32_t frame_id);

        /// 结束当前帧
        void EndFrame();

        /// 添加渲染命令
        template<typename T, typename... Args>
        void AddCommand(Args&&... args)
        {
            static_assert(std::is_base_of_v<RenderCommand, T>, "T must derive from RenderCommand");
            
            auto command = ExMakeShared<T>(std::forward<Args>(args)...);
            command->frame_id = m_current_frame_id.load();
            
            std::lock_guard<std::mutex> lock(m_queue_mutex);
            m_command_queue.push(command);
            m_stats.commands_added++;
        }

        /// 添加自定义命令
        void AddCustomCommand(std::function<void()> func, 
                             RenderCommandPriority priority = RenderCommandPriority::NORMAL,
                             uint64_t sort_key = 0);

        /// 执行所有命令
        void ExecuteCommands();

        /// 清空命令队列
        void ClearCommands();

        /// 设置命令排序
        void SetCommandSorting(bool enable) { m_enable_sorting = enable; }

        /// 获取统计信息
        struct QueueStats
        {
            uint32_t commands_added;
            uint32_t commands_executed;
            uint32_t commands_pending;
            uint32_t frames_processed;
            float average_commands_per_frame;
            std::chrono::microseconds average_execution_time;
        };
        const QueueStats& GetStats() const { return m_stats; }

    private:
        std::queue<ExSharedPtr<RenderCommand>> m_command_queue;
        std::vector<ExSharedPtr<RenderCommand>> m_sorted_commands;
        
        std::atomic<uint32_t> m_current_frame_id;
        std::atomic<bool> m_enable_sorting;
        uint32_t m_max_commands;
        
        QueueStats m_stats;
        std::mutex m_queue_mutex;
        std::mutex m_execution_mutex;

        void SortCommands();
        static bool CompareCommands(const ExSharedPtr<RenderCommand>& a, 
                                   const ExSharedPtr<RenderCommand>& b);
    };

    /// 后台资源加载器
    class UIBackgroundResourceLoader
    {
    public:
        UIBackgroundResourceLoader();
        ~UIBackgroundResourceLoader();

        /// 初始化资源加载器
        HRESULT Initialize(uint32_t worker_thread_count = 2);

        /// 关闭资源加载器
        void Shutdown();

        /// 添加资源加载任务
        template<typename T>
        std::future<T> AddLoadTask(std::function<T()> load_func, 
                                  RenderCommandPriority priority = RenderCommandPriority::NORMAL)
        {
            auto task = std::make_shared<std::packaged_task<T()>>(std::move(load_func));
            auto future = task->get_future();
            
            LoadTask load_task;
            load_task.priority = priority;
            load_task.execute_func = [task]() { (*task)(); };
            load_task.submit_time = std::chrono::steady_clock::now();
            
            {
                std::lock_guard<std::mutex> lock(m_task_mutex);
                m_task_queue.push(std::move(load_task));
                m_stats.tasks_submitted++;
            }
            
            m_task_cv.notify_one();
            return future;
        }

        /// 获取统计信息
        struct LoaderStats
        {
            uint32_t tasks_submitted;
            uint32_t tasks_completed;
            uint32_t tasks_pending;
            uint32_t worker_threads;
            std::chrono::microseconds average_load_time;
        };
        const LoaderStats& GetStats() const { return m_stats; }

    private:
        struct LoadTask
        {
            RenderCommandPriority priority;
            std::function<void()> execute_func;
            std::chrono::steady_clock::time_point submit_time;
            
            bool operator<(const LoadTask& other) const
            {
                return priority < other.priority;
            }
        };

        std::priority_queue<LoadTask> m_task_queue;
        std::vector<std::thread> m_worker_threads;
        
        std::atomic<bool> m_shutdown_requested;
        LoaderStats m_stats;
        
        std::mutex m_task_mutex;
        std::condition_variable m_task_cv;

        void WorkerThreadProc();
    };

    /// UI更新渲染分离管理器
    class UIUpdateRenderSeparator
    {
    public:
        UIUpdateRenderSeparator();
        ~UIUpdateRenderSeparator();

        /// 初始化分离管理器
        HRESULT Initialize();

        /// 关闭分离管理器
        void Shutdown();

        /// 开始UI更新阶段
        void BeginUIUpdate();

        /// 结束UI更新阶段
        void EndUIUpdate();

        /// 开始渲染阶段
        void BeginRender();

        /// 结束渲染阶段
        void EndRender();

        /// 同步UI更新和渲染
        void SynchronizeUpdateRender();

        /// 设置更新频率
        void SetUpdateFrequency(uint32_t updates_per_second);

        /// 设置渲染频率
        void SetRenderFrequency(uint32_t frames_per_second);

        /// 获取统计信息
        struct SeparatorStats
        {
            uint32_t ui_updates_per_second;
            uint32_t renders_per_second;
            std::chrono::microseconds average_update_time;
            std::chrono::microseconds average_render_time;
            float update_render_ratio;
        };
        const SeparatorStats& GetStats() const { return m_stats; }

    private:
        std::thread m_update_thread;
        std::thread m_render_thread;

        std::atomic<bool> m_shutdown_requested;
        std::atomic<bool> m_ui_update_ready;
        std::atomic<bool> m_render_ready;

        uint32_t m_target_update_frequency;
        uint32_t m_target_render_frequency;

        SeparatorStats m_stats;
        std::mutex m_sync_mutex;
        std::condition_variable m_sync_cv;

        void UpdateThreadProc();
        void RenderThreadProc();
        void UpdateStatistics();
    };

    /// 全局渲染系统实例
    extern UIRenderCommandQueue* g_render_command_queue;
    extern UIBackgroundResourceLoader* g_background_loader;
    extern UIUpdateRenderSeparator* g_update_render_separator;

    /// 便捷宏定义
    #define RENDER_COMMAND_ADD(type, ...) if(g_render_command_queue) g_render_command_queue->AddCommand<type>(__VA_ARGS__)
    #define RENDER_COMMAND_EXECUTE() if(g_render_command_queue) g_render_command_queue->ExecuteCommands()
    #define RENDER_COMMAND_BEGIN_FRAME(id) if(g_render_command_queue) g_render_command_queue->BeginFrame(id)
    #define RENDER_COMMAND_END_FRAME() if(g_render_command_queue) g_render_command_queue->EndFrame()

    #define BACKGROUND_LOAD_TASK(func, priority) if(g_background_loader) g_background_loader->AddLoadTask(func, priority)

    #define UI_UPDATE_BEGIN() if(g_update_render_separator) g_update_render_separator->BeginUIUpdate()
    #define UI_UPDATE_END() if(g_update_render_separator) g_update_render_separator->EndUIUpdate()
    #define RENDER_BEGIN() if(g_update_render_separator) g_update_render_separator->BeginRender()
    #define RENDER_END() if(g_update_render_separator) g_update_render_separator->EndRender()

    /// RAII渲染命令帧管理类
    class ScopedRenderCommandFrame
    {
    public:
        ScopedRenderCommandFrame(uint32_t frame_id)
        {
            RENDER_COMMAND_BEGIN_FRAME(frame_id);
        }

        ~ScopedRenderCommandFrame()
        {
            RENDER_COMMAND_EXECUTE();
            RENDER_COMMAND_END_FRAME();
        }
    };

    /// RAII UI更新管理类
    class ScopedUIUpdate
    {
    public:
        ScopedUIUpdate()
        {
            UI_UPDATE_BEGIN();
        }

        ~ScopedUIUpdate()
        {
            UI_UPDATE_END();
        }
    };

    /// RAII渲染管理类
    class ScopedRender
    {
    public:
        ScopedRender()
        {
            RENDER_BEGIN();
        }

        ~ScopedRender()
        {
            RENDER_END();
        }
    };

    #define RENDER_COMMAND_SCOPED_FRAME(id) ScopedRenderCommandFrame _scoped_command_frame(id)
    #define UI_UPDATE_SCOPED() ScopedUIUpdate _scoped_ui_update
    #define RENDER_SCOPED() ScopedRender _scoped_render
}
